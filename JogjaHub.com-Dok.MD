# JogjaHub - Dokumentasi Lengkap Project

## 1. Overview Project

### 1.1 Deskripsi Project
JogjaHub adalah platform mobile booking akomodasi untuk wilayah Yogyakarta yang dikembangkan menggunakan React Native Expo. Aplikasi ini menghubungkan wisatawan dengan berbagai pilihan akomodasi mulai dari homestay, guesthouse, hotel, hingga villa di Yogyakarta.

### 1.2 Unique Value Proposition
- **Fokus Lokal**: Spesialisasi 100% untuk wilayah Yogyakarta
- **Pengalaman Budaya**: Integrasi dengan wisata budaya dan kuliner Jogja
- **Community-Driven**: Mendukung UMKM dan masyarakat lokal
- **Mobile-First**: Pengalaman optimal di smartphone

### 1.3 Target Users
- **Primary**: Wisatawan domestik usia 20-45 tahun
- **Secondary**: Backpacker dan digital nomad
- **Tertiary**: Wisatawan mancanegara yang berkunjung ke Jogja

## 2. Tech Stack & Architecture

### 2.1 Frontend (React Native Expo)
```javascript
// Core Technologies
- React Native: ^0.72.0
- Expo SDK: ^49.0.0
- TypeScript: ^5.0.0
- Expo Router: File-based routing
- Expo Location: GPS dan lokasi
- Expo ImagePicker: Upload foto
- Expo Notifications: Push notifications
```

### 2.2 State Management
```javascript
// State Management Stack
- Zustand: Global state management
- React Query (TanStack Query): Server state management
- AsyncStorage: Local storage
- Context API: Theme dan user preferences
```

### 2.3 UI/UX Components
```javascript
// UI Library
- NativeBase: Component library
- React Native Elements: Additional components
- React Native Vector Icons: Icons
- React Native Maps: Peta dan lokasi
- React Native Gesture Handler: Gestures
```

### 2.4 Backend Integration
```javascript
// API & Services
- Axios: HTTP client
- RESTful API: Backend communication
- Firebase: Authentication & Push notifications
- Cloudinary: Image storage dan optimization
```

## 3. Struktur Folder Project

```
JogjaHub/
├── app/                          # Expo Router pages
│   ├── (auth)/                   # Authentication pages
│   │   ├── login.tsx
│   │   ├── register.tsx
│   │   └── forgot-password.tsx
│   ├── (tabs)/                   # Tab navigation
│   │   ├── index.tsx            # Home/Search
│   │   ├── bookings.tsx         # My Bookings
│   │   ├── favorites.tsx        # Favorites
│   │   └── profile.tsx          # Profile
│   ├── property/                 # Property pages
│   │   ├── [id].tsx             # Property detail
│   │   └── booking/[id].tsx     # Booking process
│   ├── search/                   # Search pages
│   │   ├── results.tsx          # Search results
│   │   └── filters.tsx          # Filter options
│   └── _layout.tsx              # Root layout
├── components/                   # Reusable components
│   ├── ui/                      # UI components
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Card.tsx
│   │   └── Loading.tsx
│   ├── property/                # Property components
│   │   ├── PropertyCard.tsx
│   │   ├── PropertyGallery.tsx
│   │   ├── PropertyInfo.tsx
│   │   └── ReviewCard.tsx
│   ├── booking/                 # Booking components
│   │   ├── BookingCard.tsx
│   │   ├── Calendar.tsx
│   │   └── PriceBreakdown.tsx
│   └── common/                  # Common components
│       ├── Header.tsx
│       ├── SearchBar.tsx
│       └── FilterModal.tsx
├── hooks/                       # Custom hooks
│   ├── useAuth.ts
│   ├── useProperties.ts
│   ├── useBookings.ts
│   └── useLocation.ts
├── services/                    # API services
│   ├── api.ts                   # API configuration
│   ├── authService.ts
│   ├── propertyService.ts
│   ├── bookingService.ts
│   └── notificationService.ts
├── store/                       # Zustand stores
│   ├── authStore.ts
│   ├── propertyStore.ts
│   ├── bookingStore.ts
│   └── appStore.ts
├── types/                       # TypeScript types
│   ├── auth.ts
│   ├── property.ts
│   ├── booking.ts
│   └── api.ts
├── utils/                       # Utility functions
│   ├── constants.ts
│   ├── helpers.ts
│   ├── validation.ts
│   └── storage.ts
├── assets/                      # Static assets
│   ├── images/
│   ├── icons/
│   └── fonts/
└── config/                      # Configuration
    ├── env.ts
    ├── theme.ts
    └── navigation.ts
```

## 4. Core Features & Implementasi

### 4.1 Authentication System
```typescript
// types/auth.ts
export interface User {
  id: string;
  email: string;
  name: string;
  phone: string;
  avatar?: string;
  role: 'guest' | 'host' | 'admin';
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
}

// store/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthStore extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      token: null,
      
      login: async (email, password) => {
        const response = await authService.login(email, password);
        set({ 
          user: response.user, 
          token: response.token, 
          isAuthenticated: true 
        });
      },
      
      logout: () => {
        set({ user: null, token: null, isAuthenticated: false });
      }
    }),
    {
      name: 'auth-storage',
    }
  )
);
```

### 4.2 Property Management
```typescript
// types/property.ts
export interface Property {
  id: string;
  name: string;
  type: 'homestay' | 'guesthouse' | 'hotel' | 'villa';
  description: string;
  images: string[];
  price: number;
  location: {
    address: string;
    latitude: number;
    longitude: number;
    district: string;
  };
  amenities: string[];
  rooms: number;
  bathrooms: number;
  capacity: number;
  rating: number;
  reviewCount: number;
  hostId: string;
  hostName: string;
  isActive: boolean;
  createdAt: string;
}

export interface PropertyFilters {
  type?: string[];
  priceRange?: [number, number];
  amenities?: string[];
  location?: string;
  rating?: number;
  capacity?: number;
}

// components/property/PropertyCard.tsx
import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import { router } from 'expo-router';
import { Property } from '@/types/property';

interface PropertyCardProps {
  property: Property;
  onFavoriteToggle?: (id: string) => void;
  isFavorite?: boolean;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  onFavoriteToggle,
  isFavorite = false
}) => {
  const handlePress = () => {
    router.push(`/property/${property.id}`);
  };

  return (
    <Pressable 
      onPress={handlePress}
      className="bg-white rounded-lg shadow-md mb-4 overflow-hidden"
    >
      <Image 
        source={{ uri: property.images[0] }}
        className="w-full h-48"
        resizeMode="cover"
      />
      
      <View className="p-4">
        <Text className="text-lg font-semibold text-gray-800">
          {property.name}
        </Text>
        
        <Text className="text-sm text-gray-600 mt-1">
          {property.location.district} • {property.type}
        </Text>
        
        <View className="flex-row items-center mt-2">
          <Text className="text-lg font-bold text-blue-600">
            Rp {property.price.toLocaleString('id-ID')}
          </Text>
          <Text className="text-sm text-gray-500 ml-1">/malam</Text>
        </View>
        
        <View className="flex-row items-center mt-2">
          <Text className="text-sm text-gray-600">
            ⭐ {property.rating} ({property.reviewCount} ulasan)
          </Text>
        </View>
      </View>
    </Pressable>
  );
};
```

### 4.3 Booking System
```typescript
// types/booking.ts
export interface Booking {
  id: string;
  propertyId: string;
  propertyName: string;
  propertyImage: string;
  userId: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
}

// components/booking/BookingFlow.tsx
import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Calendar } from './Calendar';
import { GuestSelector } from './GuestSelector';
import { PriceBreakdown } from './PriceBreakdown';
import { PaymentMethod } from './PaymentMethod';
import { Button } from '@/components/ui/Button';

export const BookingFlow: React.FC<{ propertyId: string }> = ({ propertyId }) => {
  const [step, setStep] = useState(1);
  const [bookingData, setBookingData] = useState({
    checkIn: '',
    checkOut: '',
    guests: 1,
    paymentMethod: 'bank_transfer'
  });

  const handleBooking = async () => {
    try {
      const result = await bookingService.createBooking({
        propertyId,
        ...bookingData
      });
      
      // Navigate to payment or confirmation
      router.push(`/booking/confirmation/${result.id}`);
    } catch (error) {
      // Handle error
    }
  };

  return (
    <ScrollView className="flex-1 bg-white">
      {step === 1 && (
        <Calendar
          onDateSelect={(checkIn, checkOut) => 
            setBookingData(prev => ({ ...prev, checkIn, checkOut }))
          }
          onNext={() => setStep(2)}
        />
      )}
      
      {step === 2 && (
        <GuestSelector
          guests={bookingData.guests}
          onGuestsChange={(guests) => 
            setBookingData(prev => ({ ...prev, guests }))
          }
          onNext={() => setStep(3)}
          onBack={() => setStep(1)}
        />
      )}
      
      {step === 3 && (
        <View className="p-4">
          <PriceBreakdown 
            propertyId={propertyId}
            checkIn={bookingData.checkIn}
            checkOut={bookingData.checkOut}
            guests={bookingData.guests}
          />
          
          <PaymentMethod
            selectedMethod={bookingData.paymentMethod}
            onMethodChange={(method) => 
              setBookingData(prev => ({ ...prev, paymentMethod: method }))
            }
          />
          
          <Button 
            title="Konfirmasi Booking"
            onPress={handleBooking}
            className="mt-6"
          />
        </View>
      )}
    </ScrollView>
  );
};
```

### 4.4 Search & Filter System
```typescript
// hooks/useProperties.ts
import { useQuery } from '@tanstack/react-query';
import { propertyService } from '@/services/propertyService';
import { PropertyFilters } from '@/types/property';

export const useProperties = (filters: PropertyFilters) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: () => propertyService.getProperties(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePropertySearch = (query: string, filters: PropertyFilters) => {
  return useQuery({
    queryKey: ['property-search', query, filters],
    queryFn: () => propertyService.searchProperties(query, filters),
    enabled: query.length > 2,
  });
};

// components/search/SearchBar.tsx
import React, { useState, useEffect } from 'react';
import { View, TextInput, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  onSearch: (query: string) => void;
  onFilterPress: () => void;
  placeholder?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  onFilterPress,
  placeholder = "Cari homestay, hotel, villa..."
}) => {
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 300);

  useEffect(() => {
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch]);

  return (
    <View className="flex-row items-center bg-gray-100 rounded-full px-4 py-3 mx-4 mb-4">
      <Ionicons name="search" size={20} color="#666" />
      
      <TextInput
        value={query}
        onChangeText={setQuery}
        placeholder={placeholder}
        className="flex-1 ml-3 text-gray-800"
        placeholderTextColor="#666"
      />
      
      <Pressable onPress={onFilterPress} className="ml-2">
        <Ionicons name="options" size={20} color="#666" />
      </Pressable>
    </View>
  );
};
```

## 5. Navigation Structure

### 5.1 App Router Configuration
```typescript
// app/_layout.tsx
import { Stack } from 'expo-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NativeBaseProvider } from 'native-base';
import { AuthProvider } from '@/context/AuthContext';

const queryClient = new QueryClient();

export default function RootLayout() {
  return (
    <QueryClientProvider client={queryClient}>
      <NativeBaseProvider>
        <AuthProvider>
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="(auth)" options={{ headerShown: false }} />
            <Stack.Screen name="property/[id]" options={{ title: 'Detail Property' }} />
            <Stack.Screen name="booking/[id]" options={{ title: 'Booking' }} />
          </Stack>
        </AuthProvider>
      </NativeBaseProvider>
    </QueryClientProvider>
  );
}

// app/(tabs)/_layout.tsx
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#6B7280',
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <Ionicons name="home" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="bookings"
        options={{
          title: 'Booking',
          tabBarIcon: ({ color }) => <Ionicons name="calendar" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="favorites"
        options={{
          title: 'Favorit',
          tabBarIcon: ({ color }) => <Ionicons name="heart" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <Ionicons name="person" size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}
```

## 6. API Integration

### 6.1 API Service Configuration
```typescript
// services/api.ts
import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'https://api.jogjahub.com';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor untuk menambahkan token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor untuk handle error
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      useAuthStore.getState().logout();
    }
    return Promise.reject(error);
  }
);

// services/propertyService.ts
import { api } from './api';
import { Property, PropertyFilters } from '@/types/property';

export const propertyService = {
  getProperties: async (filters: PropertyFilters): Promise<Property[]> => {
    const response = await api.get('/properties', { params: filters });
    return response.data.data;
  },

  getPropertyById: async (id: string): Promise<Property> => {
    const response = await api.get(`/properties/${id}`);
    return response.data.data;
  },

  searchProperties: async (query: string, filters: PropertyFilters): Promise<Property[]> => {
    const response = await api.get('/properties/search', { 
      params: { q: query, ...filters } 
    });
    return response.data.data;
  },

  getFeaturedProperties: async (): Promise<Property[]> => {
    const response = await api.get('/properties/featured');
    return response.data.data;
  },
};
```

### 6.2 API Endpoints
```
Base URL: https://api.jogjahub.com/v1

Authentication:
POST /auth/login
POST /auth/register
POST /auth/logout
POST /auth/refresh
GET  /auth/profile

Properties:
GET    /properties                    # Get all properties with filters
GET    /properties/featured           # Get featured properties
GET    /properties/search             # Search properties
GET    /properties/:id                # Get property detail
POST   /properties                    # Create property (host only)
PUT    /properties/:id                # Update property (host only)
DELETE /properties/:id                # Delete property (host only)

Bookings:
GET    /bookings                      # Get user bookings
POST   /bookings                      # Create booking
GET    /bookings/:id                  # Get booking detail
PUT    /bookings/:id                  # Update booking
DELETE /bookings/:id                  # Cancel booking

Reviews:
GET    /properties/:id/reviews        # Get property reviews
POST   /properties/:id/reviews        # Create review
PUT    /reviews/:id                   # Update review
DELETE /reviews/:id                   # Delete review

Favorites:
GET    /favorites                     # Get user favorites
POST   /favorites                     # Add to favorites
DELETE /favorites/:propertyId         # Remove from favorites

Payments:
POST   /payments/create               # Create payment
GET    /payments/:id/status           # Check payment status
POST   /payments/:id/confirm          # Confirm payment
```

## 7. State Management

### 7.1 Zustand Stores
```typescript
// store/propertyStore.ts
import { create } from 'zustand';
import { Property, PropertyFilters } from '@/types/property';

interface PropertyStore {
  properties: Property[];
  featuredProperties: Property[];
  filters: PropertyFilters;
  favorites: string[];
  
  setProperties: (properties: Property[]) => void;
  setFeaturedProperties: (properties: Property[]) => void;
  updateFilters: (filters: Partial<PropertyFilters>) => void;
  toggleFavorite: (propertyId: string) => void;
  clearFilters: () => void;
}

export const usePropertyStore = create<PropertyStore>((set, get) => ({
  properties: [],
  featuredProperties: [],
  filters: {},
  favorites: [],
  
  setProperties: (properties) => set({ properties }),
  setFeaturedProperties: (featuredProperties) => set({ featuredProperties }),
  updateFilters: (newFilters) => set((state) => ({ 
    filters: { ...state.filters, ...newFilters } 
  })),
  toggleFavorite: (propertyId) => set((state) => ({
    favorites: state.favorites.includes(propertyId)
      ? state.favorites.filter(id => id !== propertyId)
      : [...state.favorites, propertyId]
  })),
  clearFilters: () => set({ filters: {} }),
}));
```

## 8. Features Khusus Jogja

### 8.1 Integrasi Wisata Lokal
```typescript
// types/tourism.ts
export interface TourismSpot {
  id: string;
  name: string;
  type: 'cultural' | 'historical' | 'culinary' | 'nature' | 'shopping';
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  images: string[];
  rating: number;
  distance?: number; // dari property
}

// components/property/NearbyAttractions.tsx
import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { TourismSpot } from '@/types/tourism';

interface NearbyAttractionsProps {
  propertyLocation: { latitude: number; longitude: number };
  attractions: TourismSpot[];
}

export const NearbyAttractions: React.FC<NearbyAttractionsProps> = ({
  propertyLocation,
  attractions
}) => {
  return (
    <View className="mt-6">
      <Text className="text-lg font-semibold mb-4">Wisata Terdekat</Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {attractions.map((attraction) => (
          <View key={attraction.id} className="mr-4 w-48">
            <Image 
              source={{ uri: attraction.images[0] }}
              className="w-full h-32 rounded-lg"
            />
            <Text className="mt-2 font-medium">{attraction.name}</Text>
            <Text className="text-sm text-gray-600">
              {attraction.distance}km • {attraction.type}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};
```

### 8.2 Rekomendasi Kuliner
```typescript
// types/culinary.ts
export interface CulinarySpot {
  id: string;
  name: string;
  type: 'restaurant' | 'cafe' | 'street_food' | 'traditional';
  specialties: string[];
  priceRange: 'budget' | 'mid' | 'expensive';
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  rating: number;
  distance?: number;
}

// Famous Jogja culinary spots
export const JOGJA_CULINARY_SPOTS = [
  {
    name: "Gudeg Yu Djum",
    type: "traditional",
    specialties: ["Gudeg", "Ayam Kampung", "Sambal Krecek"],
    priceRange: "budget"
  },
  {
    name: "Sate Klathak Pak Pong",
    type: "street_food", 
    specialties: ["Sate Klathak", "Tongseng"],
    priceRange: "budget"
  },
  {
    name: "Angkringan Kopi Jos",
    type: "street_food",
    specialties: ["Kopi Jos", "Nasi Kucing", "Gorengan"],
    priceRange: "budget"
  }
];
```

## 9. Push Notifications

### 9.1 Notification Setup
```typescript
// services/notificationService.ts
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export const notificationService = {
  registerForPushNotifications: async () => {
    let token;
    
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      throw new Error('Failed to get push token for push notification!');
    }
    
    token = (await Notifications.getExpoPushTokenAsync()).data;
    return token;
  },

  scheduleBookingReminder: async (bookingId: string, checkInDate: string) => {
    const checkIn = new Date(checkInDate);
    const reminderTime = new Date(checkIn.getTime() - 24 * 60 * 60 * 1000); // 1 day before
    
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Reminder Check-in',
        body: 'Jangan lupa check-in besok ya!',
        data: { bookingId },
      },
      trigger: {
        date: reminderTime,
      },
    });
  },
};
```

## 10. Performance Optimization

### 10.1 Image Optimization
```typescript
// components/ui/OptimizedImage.tsx
import React, { useState } from 'react';
import { Image, View, ActivityIndicator } from 'react-native';
import { Image as ExpoImage } from 'expo-image';

interface OptimizedImageProps {
  source: { uri: string };
  className?: string;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  className,
  placeholder = 'https://via.placeholder.com/400x300',
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  return (
    <View className={className}>
      {isLoading && (
        <View className="absolute inset-0 items-center justify-center bg-gray-100">
          <ActivityIndicator size="small" color="#3B82F6" />
        </View>
      )}
      
      <ExpoImage
        source={hasError ? { uri: placeholder } : source}
        className={className}
        onLoad={handleLoad}
        onError={handleError}
        cachePolicy="memory-disk"
        transition={200}
      />
    </View>
  );
};
```

### 10.2 List Performance
```typescript
// components/property/PropertyList.tsx
import React, { useCallback } from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { PropertyCard } from './PropertyCard';
import { Property } from '@/types/property';

interface PropertyListProps {
  properties: Property[];
  onRefresh: () => void;
  refreshing: boolean;
  onEndReached?: () => void;
  loading?: boolean;
}

export const PropertyList: React.FC<PropertyListProps> = ({
  properties,
  onRefresh,
  refreshing,
  onEndReached,
  loading = false
}) => {
  const renderItem = useCallback(({ item }: { item: Property }) => (
    <PropertyCard property={item} />
  ), []);

  const keyExtractor = useCallback((item: Property) => item.id, []);

  return (
    <FlatList
      data={properties}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
      onEndReached={onEndReached}
      onEndReachedThreshold={0.1}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      getItemLayout={(data, index) => ({
        length: 280, // estimated item height
        offset: 280 * index,
        index,
      })}
    />
  );
};
```

## 11. Testing Strategy

### 11.1 Unit Testing Setup
```typescript
// jest.config.js
module.exports = {
  preset: 'jest-expo',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*): '<rootDir>/$1',
  },
  collectCoverageFrom: [
    'components/**/*.{ts,tsx}',
    'hooks/**/*.{ts,tsx}',
    'services/**/*.{ts,tsx}',
    'utils/**/*.{ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};

// jest.setup.js
import '@testing-library/jest-native/extend-expect';
import 'react-native-gesture-handler/jestSetup';

// Mock Expo modules
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(),
  getCurrentPositionAsync: jest.fn(),
}));

jest.mock('expo-notifications', () => ({
  getPermissionsAsync: jest.fn(),
  requestPermissionsAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
}));
```

### 11.2 Component Testing
```typescript
// __tests__/components/PropertyCard.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PropertyCard } from '@/components/property/PropertyCard';
import { Property } from '@/types/property';

const mockProperty: Property = {
  id: '1',
  name: 'Homestay Malioboro',
  type: 'homestay',
  description: 'Homestay nyaman di pusat kota',
  images: ['https://example.com/image1.jpg'],
  price: 150000,
  location: {
    address: 'Jl. Malioboro No. 1',
    latitude: -7.7956,
    longitude: 110.3695,
    district: 'Gedong Tengen',
  },
  amenities: ['WiFi', 'AC', 'Breakfast'],
  rooms: 2,
  bathrooms: 1,
  capacity: 4,
  rating: 4.5,
  reviewCount: 23,
  hostId: 'host1',
  hostName: 'Pak Budi',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
};

describe('PropertyCard', () => {
  it('renders property information correctly', () => {
    const { getByText } = render(<PropertyCard property={mockProperty} />);
    
    expect(getByText('Homestay Malioboro')).toBeTruthy();
    expect(getByText('Gedong Tengen • homestay')).toBeTruthy();
    expect(getByText('Rp 150.000')).toBeTruthy();
    expect(getByText('⭐ 4.5 (23 ulasan)')).toBeTruthy();
  });

  it('handles press events correctly', () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <PropertyCard property={mockProperty} onPress={mockOnPress} />
    );
    
    fireEvent.press(getByTestId('property-card'));
    expect(mockOnPress).toHaveBeenCalledWith(mockProperty.id);
  });
});
```

### 11.3 Hook Testing
```typescript
// __tests__/hooks/useProperties.test.tsx
import { renderHook, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useProperties } from '@/hooks/useProperties';
import { propertyService } from '@/services/propertyService';

// Mock the service
jest.mock('@/services/propertyService');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useProperties', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches properties successfully', async () => {
    const mockProperties = [mockProperty];
    (propertyService.getProperties as jest.Mock).mockResolvedValue(mockProperties);

    const { result } = renderHook(() => useProperties({}), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockProperties);
  });

  it('handles error states', async () => {
    (propertyService.getProperties as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    const { result } = renderHook(() => useProperties({}), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(new Error('API Error'));
  });
});
```

### 11.4 Integration Testing
```typescript
// __tests__/integration/BookingFlow.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { BookingFlow } from '@/components/booking/BookingFlow';
import { bookingService } from '@/services/bookingService';

jest.mock('@/services/bookingService');

describe('BookingFlow Integration', () => {
  it('completes booking flow successfully', async () => {
    const mockBooking = {
      id: 'booking1',
      propertyId: 'property1',
      checkIn: '2024-03-01',
      checkOut: '2024-03-03',
      guests: 2,
      totalPrice: 300000,
      status: 'pending',
    };

    (bookingService.createBooking as jest.Mock).mockResolvedValue(mockBooking);

    const { getByText, getByTestId } = render(
      <BookingFlow propertyId="property1" />
    );

    // Step 1: Select dates
    fireEvent.press(getByTestId('date-picker-checkin'));
    fireEvent.press(getByTestId('date-2024-03-01'));
    fireEvent.press(getByTestId('date-picker-checkout'));
    fireEvent.press(getByTestId('date-2024-03-03'));
    fireEvent.press(getByText('Lanjut'));

    // Step 2: Select guests
    fireEvent.press(getByTestId('guest-increment'));
    fireEvent.press(getByText('Lanjut'));

    // Step 3: Confirm booking
    fireEvent.press(getByText('Konfirmasi Booking'));

    await waitFor(() => {
      expect(bookingService.createBooking).toHaveBeenCalledWith({
        propertyId: 'property1',
        checkIn: '2024-03-01',
        checkOut: '2024-03-03',
        guests: 2,
        paymentMethod: 'bank_transfer',
      });
    });
  });
});
```

## 12. Deployment & CI/CD

### 12.1 Environment Configuration
```typescript
// config/env.ts
export const ENV = {
  API_URL: process.env.EXPO_PUBLIC_API_URL || 'https://api.jogjahub.com',
  GOOGLE_MAPS_API_KEY: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || '',
  FIREBASE_CONFIG: {
    apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY || '',
    authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
    projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || '',
    storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID || '',
  },
  CLOUDINARY_CLOUD_NAME: process.env.EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  SENTRY_DSN: process.env.EXPO_PUBLIC_SENTRY_DSN || '',
};

// app.config.js
export default {
  expo: {
    name: 'JogjaHub',
    slug: 'jogjahub',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.jogjahub.app',
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#ffffff',
      },
      package: 'com.jogjahub.app',
      permissions: [
        'ACCESS_FINE_LOCATION',
        'ACCESS_COARSE_LOCATION',
        'CAMERA',
        'WRITE_EXTERNAL_STORAGE',
      ],
    },
    web: {
      favicon: './assets/favicon.png',
    },
    plugins: [
      'expo-location',
      'expo-image-picker',
      'expo-notifications',
      [
        'expo-build-properties',
        {
          android: {
            compileSdkVersion: 34,
            targetSdkVersion: 34,
            buildToolsVersion: '34.0.0',
          },
          ios: {
            deploymentTarget: '11.0',
          },
        },
      ],
    ],
    extra: {
      eas: {
        projectId: 'your-project-id',
      },
    },
  },
};
```

### 12.2 EAS Build Configuration
```json
// eas.json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "staging": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_API_URL": "https://staging-api.jogjahub.com"
      }
    },
    "production": {
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api.jogjahub.com"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "your-app-store-connect-app-id",
        "appleTeamId": "your-apple-team-id"
      },
      "android": {
        "serviceAccountKeyPath": "./service-account-key.json",
        "track": "production"
      }
    }
  }
}
```

### 12.3 GitHub Actions CI/CD
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  build-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build staging
        run: eas build --platform all --profile staging --non-interactive

  build-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build production
        run: eas build --platform all --profile production --non-interactive
      
      - name: Submit to stores
        run: eas submit --platform all --profile production --non-interactive
```

## 13. Monitoring & Analytics

### 13.1 Error Tracking dengan Sentry
```typescript
// config/sentry.ts
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: process.env.EXPO_PUBLIC_SENTRY_DSN,
  environment: __DEV__ ? 'development' : 'production',
  tracesSampleRate: 1.0,
});

export const captureError = (error: Error, context?: any) => {
  if (context) {
    Sentry.setContext('error_context', context);
  }
  Sentry.captureException(error);
};

export const captureMessage = (message: string, level: 'info' | 'warning' | 'error' = 'info') => {
  Sentry.captureMessage(message, level);
};

// App.tsx
import { withSentry } from '@sentry/react-native';

function App() {
  return <RootNavigator />;
}

export default withSentry(App);
```

### 13.2 Analytics dengan Firebase
```typescript
// services/analyticsService.ts
import analytics from '@react-native-firebase/analytics';

export const analyticsService = {
  logEvent: async (eventName: string, parameters: Record<string, any>) => {
    await analytics().logEvent(eventName, parameters);
  },

  logScreenView: async (screenName: string) => {
    await analytics().logScreenView({
      screen_name: screenName,
      screen_class: screenName,
    });
  },

  logBookingAttempt: async (propertyId: string, propertyType: string) => {
    await analytics().logEvent('booking_attempt', {
      property_id: propertyId,
      property_type: propertyType,
    });
  },

  logBookingSuccess: async (bookingId: string, totalPrice: number) => {
    await analytics().logEvent('booking_success', {
      booking_id: bookingId,
      value: totalPrice,
      currency: 'IDR',
    });
  },

  logSearch: async (query: string, filters: any) => {
    await analytics().logEvent('search', {
      search_term: query,
      filters: JSON.stringify(filters),
    });
  },

  setUserProperty: async (property: string, value: string) => {
    await analytics().setUserProperty(property, value);
  },
};
```

## 14. Security Implementation

### 14.1 Security Headers & Validation
```typescript
// utils/security.ts
export const sanitizeInput = (input: string): string => {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
  return phoneRegex.test(phone);
};

export const validatePassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

// Security middleware untuk API calls
export const securityMiddleware = (config: any) => {
  // Add security headers
  config.headers = {
    ...config.headers,
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Security-Policy': "default-src 'self'",
  };
  
  // Sanitize request data
  if (config.data) {
    config.data = sanitizeRequestData(config.data);
  }
  
  return config;
};
```

### 14.2 Secure Storage
```typescript
// services/secureStorage.ts
import * as SecureStore from 'expo-secure-store';

export const secureStorage = {
  setItem: async (key: string, value: string): Promise<void> => {
    await SecureStore.setItemAsync(key, value);
  },

  getItem: async (key: string): Promise<string | null> => {
    return await SecureStore.getItemAsync(key);
  },

  removeItem: async (key: string): Promise<void> => {
    await SecureStore.deleteItemAsync(key);
  },

  // Secure token storage
  setAuthToken: async (token: string): Promise<void> => {
    await SecureStore.setItemAsync('auth_token', token);
  },

  getAuthToken: async (): Promise<string | null> => {
    return await SecureStore.getItemAsync('auth_token');
  },

  removeAuthToken: async (): Promise<void> => {
    await SecureStore.deleteItemAsync('auth_token');
  },
};
```

## 15. Performance Monitoring

### 15.1 Performance Metrics
```typescript
// services/performanceService.ts
import { startTransition } from 'react';
import { performance } from 'perf_hooks';

export const performanceService = {
  measureApiCall: async <T>(
    apiCall: () => Promise<T>,
    endpoint: string
  ): Promise<T> => {
    const start = performance.now();
    
    try {
      const result = await apiCall();
      const end = performance.now();
      
      // Log performance metrics
      console.log(`API Call ${endpoint}: ${end - start}ms`);
      
      // Send to analytics if needed
      analyticsService.logEvent('api_performance', {
        endpoint,
        duration: end - start,
        status: 'success',
      });
      
      return result;
    } catch (error) {
      const end = performance.now();
      
      analyticsService.logEvent('api_performance', {
        endpoint,
        duration: end - start,
        status: 'error',
      });
      
      throw error;
    }
  },

  measureComponentRender: (componentName: string, renderFunction: () => void) => {
    const start = performance.now();
    
    startTransition(() => {
      renderFunction();
      
      const end = performance.now();
      console.log(`Component ${componentName} render: ${end - start}ms`);
    });
  },
};
```

## 16. Offline Support

### 16.1 Offline Storage Strategy
```typescript
// services/offlineService.ts
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';

export const offlineService = {
  isOnline: async (): Promise<boolean> => {
    const netInfo = await NetInfo.fetch();
    return netInfo.isConnected ?? false;
  },

  cacheData: async (key: string, data: any): Promise<void> => {
    const cacheData = {
      data,
      timestamp: Date.now(),
    };
    await AsyncStorage.setItem(key, JSON.stringify(cacheData));
  },

  getCachedData: async (key: string, maxAge: number = 300000): Promise<any | null> => {
    const cached = await AsyncStorage.getItem(key);
    if (!cached) return null;
    
    const { data, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp > maxAge) {
      await AsyncStorage.removeItem(key);
      return null;
    }
    
    return data;
  },

  syncPendingRequests: async (): Promise<void> => {
    const isOnline = await offlineService.isOnline();
    if (!isOnline) return;
    
    const pendingRequests = await AsyncStorage.getItem('pending_requests');
    if (!pendingRequests) return;
    
    const requests = JSON.parse(pendingRequests);
    
    for (const request of requests) {
      try {
        await api.request(request);
      } catch (error) {
        console.error('Failed to sync request:', error);
      }
    }
    
    await AsyncStorage.removeItem('pending_requests');
  },
};
```

## 17. Accessibility

### 17.1 Accessibility Implementation
```typescript
// components/ui/AccessibleButton.tsx
import React from 'react';
import { Pressable, Text, AccessibilityRole } from 'react-native';

interface AccessibleButtonProps {
  title: string;
  onPress: () => void;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  disabled?: boolean;
  role?: AccessibilityRole;
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  title,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  disabled = false,
  role = 'button',
}) => {
  return (
    <Pressable
      onPress={onPress}
      disabled={disabled}
      accessible={true}
      accessibilityRole={role}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        disabled,
      }}
      className={`px-4 py-2 rounded-lg ${
        disabled ? 'bg-gray-300' : 'bg-blue-600'
      }`}
    >
      <Text className={`text-center font-medium ${
        disabled ? 'text-gray-500' : 'text-white'
      }`}>
        {title}
      </Text>
    </Pressable>
  );
};
```

## 18. Development Workflow

### 18.1 Git Workflow
```bash
# Branch naming convention
feature/auth-login
bugfix/booking-date-validation
hotfix/critical-payment-issue
release/v1.0.0

# Commit message convention
feat: add user authentication
fix: resolve booking date validation
docs: update API documentation
style: format code with prettier
refactor: optimize property search
test: add unit tests for booking service
```

### 18.2 Development Scripts
```json
// package.json scripts
{
  "scripts": {
    "start": "expo start",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web",
    "build:android": "eas build --platform android",
    "build:ios": "eas build --platform ios",
    "build:all": "eas build --platform all",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "prepare": "husky install"
  }
}
```

## 19. Business Logic Implementation

### 19.1 Pricing Calculator
```typescript
// services/pricingService.ts
export interface PricingCalculation {
  basePrice: number;
  nights: number;
  subtotal: number;
  taxes: number;
  serviceFee: number;
  total: number;
  breakdown: {
    [key: string]: number;
  };
}

export const pricingService = {
  calculateBookingPrice: (
    basePrice: number,
    checkIn: string,
    checkOut: string,
    guests: number = 1
  ): PricingCalculation => {
    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    const nights = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const subtotal = basePrice * nights;
    const serviceFee = subtotal * 0.05; // 5% service fee
    const taxes = subtotal * 0.10; // 10% tax
    const total = subtotal + serviceFee + taxes;
    
    return {
      basePrice,
      nights,
      subtotal,
      taxes,
      serviceFee,
      total,
      breakdown: {
        'Harga per malam': basePrice,
        'Jumlah malam': nights,
        'Subtotal': subtotal,
        'Biaya layanan': serviceFee,
        'Pajak': taxes,
      },
    };
  },

  applyDiscount: (pricing: PricingCalculation, discountCode?: string): PricingCalculation => {
    let discountAmount = 0;
    
    if (discountCode) {
      // Apply discount logic
      switch (discountCode) {
        case 'JOGJA10':
          discountAmount = pricing.subtotal * 0.10;
          break;
        case 'NEWUSER':
          discountAmount = Math.min(50000, pricing.subtotal * 0.15);
          break;
      }
    }
    
    const total = pricing.total - discountAmount;
    
    return {
      ...pricing,
      total,
      breakdown: {
        ...pricing.breakdown,
        ...(discountAmount > 0 && { 'Diskon': -discountAmount }),
      },
    };
  },
};
```

## 20. Launch Preparation

### 20.1 Pre-Launch Checklist
```markdown
## Technical Checklist
- [ ] All API endpoints tested and working
- [ ] Push notifications configured
- [ ] Payment integration tested
- [ ] Error handling implemented
- [ ] Performance optimized
- [ ] Security measures in place
- [ ] Offline functionality tested
- [ ] App store assets prepared
- [ ] Privacy policy and terms of service ready

## Content Checklist
- [ ] Property listings populated
- [ ] Featured properties selected
- [ ] Tourism spots database complete
- [ ] Culinary recommendations added
- [ ] Help documentation written
- [ ] FAQ section complete

## Business Checklist
- [ ] Payment gateway activated
- [ ] Bank account for settlements ready
- [ ] Customer support channels set up
- [ ] Marketing materials prepared
- [ ] Social media accounts created
- [ ] Analytics tracking configured
- [ ] Feedback collection system ready
```

### 20.2 Launch Strategy
```typescript
// config/featureFlags.ts
export const FEATURE_FLAGS = {
  PAYMENT_ENABLED: true,
  PUSH_NOTIFICATIONS: true,
  SOCIAL_LOGIN: false,
  REVIEW_SYSTEM: true,
  CHAT_SUPPORT: false,
  LOYALTY_PROGRAM: false,
};

// Gradual rollout strategy
export const ROLLOUT_CONFIG = {
  BETA_USERS: ['<EMAIL>'],
  FEATURE_ROLLOUT_PERCENTAGE: 10, // Start with 10% of users
  REGIONS: ['yogyakarta'], // Start with Yogyakarta only
};
```

---

## Summary

JogjaHub adalah aplikasi mobile booking akomodasi yang dibangun dengan React Native Expo, dirancang khusus untuk pasar Yogyakarta. Dokumentasi ini mencakup semua aspek pengembangan dari arsitektur teknis hingga strategi peluncuran.

**Key Features:**
- Mobile-first design dengan React Native Expo
- Real-time booking system
- Integrasi payment gateway
- Offline support
- Push notifications
- Local tourism integration
- Performance optimization
- Comprehensive testing

**Tech Stack:**
- React Native + Expo
- TypeScript
- Zustand + React Query
- Firebase
- RESTful API
- Secure storage

Dokumentasi ini dapat digunakan sebagai panduan lengkap untuk AI Builder dalam mengembangkan aplikasi serupa dengan mempertimbangkan best practices, security, performance, dan user experience.