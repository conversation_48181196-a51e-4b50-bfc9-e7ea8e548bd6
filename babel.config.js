module.exports = function(api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', { jsxImportSource: 'react' }]
    ],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./'],
          alias: {
            '@': './',
            '@data': './data',
            '@types': './types',
            '@services': './services',
            '@utils': './utils',
          },
        },
      ],
    ],
  };
};
