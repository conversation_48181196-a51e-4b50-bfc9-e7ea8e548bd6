# JogjaHub Mock Data

This directory contains comprehensive mock/dummy data for the JogjaHub MVP application to enable full functionality testing without requiring a backend API.

## Data Structure

### Core Data Files
- `users.ts` - Sample user profiles with Indonesian names and local context
- `properties.ts` - Property listings with realistic Yogyakarta locations
- `bookings.ts` - Booking history with various statuses and scenarios
- `reviews.ts` - Authentic Indonesian language reviews with ratings
- `tourism.ts` - Yogyakarta tourism spots and cultural attractions
- `culinary.ts` - Local food spots and culinary recommendations

### Location Data
- `districts.ts` - Yogyakarta districts with coordinates and characteristics
- `attractions.ts` - Cultural sites, historical places, and points of interest
- `coordinates.ts` - Accurate GPS coordinates for all locations

### Configuration
- `amenities.ts` - Common Indonesian accommodation amenities
- `constants.ts` - App constants and configuration values
- `generators.ts` - Utility functions for generating realistic data

## Data Features

### Authenticity
- All data reflects Indonesian culture and local context
- Realistic pricing in Indonesian Rupiah (IDR)
- Authentic Indonesian names and phone numbers
- Local addresses and district names
- Cultural integration with Jogja-specific elements

### Completeness
- 15+ diverse property listings
- 20+ user profiles (guests and hosts)
- 50+ booking records with various statuses
- 100+ reviews in Indonesian language
- 30+ tourism spots and attractions
- 25+ culinary recommendations

### Relationships
- Proper data relationships between entities
- Consistent IDs and references
- Realistic booking-to-review ratios
- Host-property associations
- User-booking-review chains

## Usage

Import the data in your components or services:

```typescript
import { mockProperties } from '@/data/properties';
import { mockUsers } from '@/data/users';
import { mockBookings } from '@/data/bookings';
```

## Data Validation

All mock data includes:
- Type safety with TypeScript interfaces
- Data validation functions
- Consistency checks
- Realistic constraints and relationships
