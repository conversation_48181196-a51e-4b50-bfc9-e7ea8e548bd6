// Common Indonesian accommodation amenities
export const commonAmenities = [
  // Basic Amenities
  'WiFi Gratis',
  'AC',
  'Fan/Kipas Angin',
  'TV',
  '<PERSON><PERSON>',
  '<PERSON><PERSON> Luar',
  'Air Panas',
  'Handuk',
  'Sabun & Shampoo',
  'Tissue',

  // Comfort Amenities
  '<PERSON><PERSON><PERSON>',
  'Bantal & Guling',
  'Se<PERSON>ut',
  '<PERSON><PERSON><PERSON>',
  'Meja & Kursi',
  '<PERSON>rmin',
  'Gantungan Baju',
  'Stop Kontak',
  'Lampu Baca',

  // Kitchen & Dining
  'Dapur Lengkap',
  'Kulkas',
  'Kompor Gas',
  'Peralatan Masak',
  'Piring & Gelas',
  'Dispenser Air',
  'Teko Listrik',
  'Microwave',
  'Rice Cooker',

  // Food & Beverage
  'Sarapan',
  'Sarapan Tradisional',
  'Sarapan Continental',
  'Kopi & Teh Gratis',
  'Air Mineral Gratis',
  'Snack Gratis',
  'Welcome Drink',

  // Transportation & Parking
  'Parkir Motor',
  'Parkir Mobil',
  'Parkir Gratis',
  'Antar Jemput Bandara',
  'Rental Motor',
  'Rental Sepeda',
  'Ojek Online Access',

  // Entertainment & Recreation
  'TV Kabel',
  'Netflix',
  'Sound System',
  'Karaoke',
  'Permainan Tradisional',
  'Buku & Majalah',
  'Board Games',

  // Outdoor & Garden
  'Taman',
  'Teras',
  'Gazebo',
  'BBQ Area',
  'Kolam Renang',
  'Kolam Ikan',
  'Tempat Jemuran',

  // Services
  'Laundry',
  'Setrika',
  'Room Service',
  'Concierge',
  'Tour Guide',
  'Travel Information',
  'Penitipan Barang',
  'Security 24 Jam',

  // Business & Work
  'Meeting Room',
  'Workspace',
  'Printer',
  'Fax',
  'High Speed Internet',
  'Video Conference',

  // Health & Wellness
  'First Aid Kit',
  'Spa',
  'Massage',
  'Gym',
  'Yoga Area',

  // Family & Kids
  'Family Room',
  'Kids Area',
  'Baby Crib',
  'High Chair',
  'Playground',
  'Kids Pool',

  // Special Features
  'Pemandangan Gunung',
  'Pemandangan Sawah',
  'Pemandangan Kota',
  'Sunrise View',
  'Sunset View',
  'Heritage Building',
  'Joglo Architecture',
  'Modern Design',
  'Minimalist Style',
  'Traditional Decor'
];

// Jogja-specific cultural amenities
export const jogjaSpecificAmenities = [
  // Cultural Experience
  'Belajar Membatik',
  'Workshop Kerajinan',
  'Kelas Memasak Gudeg',
  'Cerita Sejarah Jogja',
  'Traditional Dance Show',
  'Gamelan Performance',
  'Wayang Show',

  // Cultural Architecture
  'Joglo Architecture',
  'Limasan Style',
  'Pendopo',
  'Traditional Garden',
  'Javanese Interior',
  'Antique Furniture',
  'Traditional Art Display',

  // Local Food & Beverage
  'Gudeg Breakfast',
  'Kopi Joss',
  'Wedang Ronde',
  'Jamu Tradisional',
  'Local Snacks',
  'Traditional Tea Ceremony',

  // Proximity to Cultural Sites
  'Walking Distance to Malioboro',
  'Near Kraton',
  'Close to Taman Sari',
  'Near Alun-alun',
  'Close to Traditional Market',
  'Near Batik Gallery',
  'Walking to Cultural Sites',

  // Local Transportation
  'Becak Service',
  'Andong Ride',
  'Traditional Transport Info',
  'Local Guide Service',

  // Cultural Activities
  'Batik Shopping Guide',
  'Silver Craft Tour',
  'Traditional Market Tour',
  'Cultural Site Guide',
  'Local Culinary Tour',
  'Photography Spots Info'
];

// Amenity categories for filtering
export const amenityCategories = {
  basic: [
    'WiFi Gratis', 'AC', 'TV', 'Kamar Mandi Dalam', 'Air Panas'
  ],
  comfort: [
    'Kasur Nyaman', 'Lemari Pakaian', 'Meja & Kursi', 'Kulkas'
  ],
  food: [
    'Sarapan', 'Dapur Lengkap', 'Kopi & Teh Gratis', 'Air Mineral Gratis'
  ],
  transport: [
    'Parkir Motor', 'Parkir Mobil', 'Antar Jemput Bandara', 'Rental Motor'
  ],
  entertainment: [
    'TV Kabel', 'Netflix', 'Sound System', 'Karaoke'
  ],
  outdoor: [
    'Taman', 'Teras', 'Gazebo', 'BBQ Area', 'Kolam Renang'
  ],
  services: [
    'Laundry', 'Room Service', 'Concierge', 'Security 24 Jam'
  ],
  business: [
    'Meeting Room', 'Workspace', 'High Speed Internet', 'Printer'
  ],
  family: [
    'Family Room', 'Kids Area', 'Baby Crib', 'Playground'
  ],
  cultural: [
    'Belajar Membatik', 'Joglo Architecture', 'Gudeg Breakfast', 'Walking Distance to Malioboro'
  ]
};

// Property type specific amenities
export const propertyTypeAmenities = {
  homestay: [
    'WiFi Gratis', 'AC', 'Sarapan Tradisional', 'Parkir Motor', 
    'Taman', 'Cerita Sejarah Jogja', 'Kopi & Teh Gratis', 'Family Room'
  ],
  guesthouse: [
    'WiFi Gratis', 'AC', 'Common Area', 'Shared Kitchen', 'Laundry',
    'Travel Information', 'Loker', 'Bike Rental'
  ],
  hotel: [
    'WiFi Gratis', 'AC', 'Room Service', 'Concierge', 'Restaurant',
    'Spa', 'Gym', 'Airport Shuttle', 'Meeting Room'
  ],
  villa: [
    'WiFi Gratis', 'AC', 'Kolam Renang', 'Dapur Lengkap', 'BBQ Area',
    'Taman', 'Parkir Mobil', 'Sound System', 'Meeting Room'
  ]
};

// Price range specific amenities
export const priceRangeAmenities = {
  budget: [
    'WiFi Gratis', 'Fan/Kipas Angin', 'Kamar Mandi Luar', 'Parkir Motor',
    'Air Mineral Gratis', 'Travel Information'
  ],
  mid: [
    'WiFi Gratis', 'AC', 'TV', 'Kamar Mandi Dalam', 'Air Panas',
    'Sarapan', 'Parkir Gratis', 'Laundry'
  ],
  luxury: [
    'WiFi Gratis', 'AC', 'TV Kabel', 'Kamar Mandi Dalam', 'Air Panas',
    'Room Service', 'Spa', 'Kolam Renang', 'Concierge', 'Airport Shuttle'
  ]
};

// Export all amenities combined
export const allAmenities = [...commonAmenities, ...jogjaSpecificAmenities];

// Export functions to get amenities by category
export const getAmenitiesByCategory = (category: keyof typeof amenityCategories) => {
  return amenityCategories[category] || [];
};

export const getAmenitiesByPropertyType = (type: keyof typeof propertyTypeAmenities) => {
  return propertyTypeAmenities[type] || [];
};

export const getAmenitiesByPriceRange = (range: keyof typeof priceRangeAmenities) => {
  return priceRangeAmenities[range] || [];
};

// Default export
export default {
  commonAmenities,
  jogjaSpecificAmenities,
  amenityCategories,
  propertyTypeAmenities,
  priceRangeAmenities,
  allAmenities
};
