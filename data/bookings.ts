import { Booking } from '../types/booking';

export const mockBookings: Booking[] = [
  {
    id: 'booking_001',
    propertyId: 'prop_001',
    propertyName: 'Homestay Malioboro Heritage',
    propertyImage: 'https://images.unsplash.com/photo-*************-6a8506099945?w=800',
    propertyType: 'homestay',
    userId: 'user_001',
    userName: 'And<PERSON> Pratama',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-02-15',
    checkOut: '2024-02-17',
    guests: 2,
    totalPrice: 330000,
    status: 'completed',
    paymentStatus: 'paid',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Mohon disiapkan sarapan tradisional Jogja',
    createdAt: '2024-02-10T08:30:00Z',
    updatedAt: '2024-02-17T12:00:00Z',
    confirmedAt: '2024-02-10T14:20:00Z',
    priceBreakdown: {
      basePrice: 150000,
      nights: 2,
      subtotal: 300000,
      serviceFee: 15000,
      taxes: 15000,
      cleaningFee: 0,
      discount: 0,
      total: 330000
    },
    guestDetails: {
      adults: 2,
      children: 0,
      infants: 0
    },
    contactInfo: {
      emergencyContact: '+*************',
      estimatedArrival: '15:00'
    }
  },
  {
    id: 'booking_002',
    propertyId: 'prop_002',
    propertyName: 'Villa Tugu Jogja',
    propertyImage: 'https://images.unsplash.com/photo-*************-7fde63acd811?w=800',
    propertyType: 'villa',
    userId: 'user_002',
    userName: 'Sinta Maharani',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-03-01',
    checkOut: '2024-03-03',
    guests: 8,
    totalPrice: 2120000,
    status: 'confirmed',
    paymentStatus: 'paid',
    paymentMethod: 'credit_card',
    specialRequests: 'Family gathering, mohon disiapkan BBQ area',
    createdAt: '2024-02-20T10:15:00Z',
    updatedAt: '2024-02-20T16:30:00Z',
    confirmedAt: '2024-02-20T16:30:00Z',
    priceBreakdown: {
      basePrice: 850000,
      nights: 2,
      subtotal: 1700000,
      serviceFee: 170000,
      taxes: 170000,
      cleaningFee: 80000,
      discount: 0,
      total: 2120000
    },
    guestDetails: {
      adults: 6,
      children: 2,
      infants: 0
    },
    contactInfo: {
      emergencyContact: '+6281234567891',
      estimatedArrival: '14:00'
    }
  },
  {
    id: 'booking_003',
    propertyId: 'prop_003',
    propertyName: 'Guesthouse Prawirotaman Backpacker',
    propertyImage: 'https://images.unsplash.com/photo-**********-bab0e564b8d5?w=800',
    propertyType: 'guesthouse',
    userId: 'user_003',
    userName: 'Reza Firmansyah',
    userEmail: '<EMAIL>',
    userPhone: '+6281234567896',
    checkIn: '2024-02-25',
    checkOut: '2024-02-28',
    guests: 1,
    totalPrice: 270000,
    status: 'completed',
    paymentStatus: 'paid',
    paymentMethod: 'e_wallet',
    specialRequests: 'Backpacker, butuh info wisata murah',
    createdAt: '2024-02-22T14:20:00Z',
    updatedAt: '2024-02-28T11:00:00Z',
    confirmedAt: '2024-02-22T15:45:00Z',
    priceBreakdown: {
      basePrice: 75000,
      nights: 3,
      subtotal: 225000,
      serviceFee: 22500,
      taxes: 22500,
      cleaningFee: 0,
      discount: 0,
      total: 270000
    },
    guestDetails: {
      adults: 1,
      children: 0,
      infants: 0
    },
    contactInfo: {
      estimatedArrival: '16:00'
    }
  },
  {
    id: 'booking_004',
    propertyId: 'prop_004',
    propertyName: 'Hotel Boutique Kotagede Heritage',
    propertyImage: 'https://images.unsplash.com/photo-**********-ff40c63fe5fa?w=800',
    propertyType: 'hotel',
    userId: 'user_004',
    userName: 'Maya Sari',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-03-10',
    checkOut: '2024-03-12',
    guests: 2,
    totalPrice: 990000,
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Content creator, butuh spot foto yang bagus',
    createdAt: '2024-03-05T09:30:00Z',
    updatedAt: '2024-03-05T09:30:00Z',
    priceBreakdown: {
      basePrice: 450000,
      nights: 2,
      subtotal: 900000,
      serviceFee: 45000,
      taxes: 45000,
      cleaningFee: 0,
      discount: 0,
      total: 990000
    },
    guestDetails: {
      adults: 2,
      children: 0,
      infants: 0
    },
    contactInfo: {
      estimatedArrival: '15:30'
    }
  },
  {
    id: 'booking_005',
    propertyId: 'prop_005',
    propertyName: 'Homestay Kaliurang Mountain View',
    propertyImage: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=800',
    propertyType: 'homestay',
    userId: 'user_005',
    userName: 'Budi Setiawan',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-03-15',
    checkOut: '2024-03-17',
    guests: 4,
    totalPrice: 540000,
    status: 'confirmed',
    paymentStatus: 'paid',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Healing trip, mohon info tempat hiking',
    createdAt: '2024-03-08T16:45:00Z',
    updatedAt: '2024-03-08T18:20:00Z',
    confirmedAt: '2024-03-08T18:20:00Z',
    priceBreakdown: {
      basePrice: 200000,
      nights: 2,
      subtotal: 400000,
      serviceFee: 40000,
      taxes: 40000,
      cleaningFee: 60000,
      discount: 0,
      total: 540000
    },
    guestDetails: {
      adults: 4,
      children: 0,
      infants: 0
    },
    contactInfo: {
      emergencyContact: '+*************',
      estimatedArrival: '14:30'
    }
  },
  {
    id: 'booking_006',
    propertyId: 'prop_006',
    propertyName: 'Villa Bantul Countryside',
    propertyImage: 'https://images.unsplash.com/photo-*************-4d197d17c90a?w=800',
    propertyType: 'villa',
    userId: 'user_006',
    userName: 'Dewi Lestari',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-01-20',
    checkOut: '2024-01-22',
    guests: 12,
    totalPrice: 1860000,
    status: 'completed',
    paymentStatus: 'paid',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Retreat guru, butuh meeting room',
    createdAt: '2024-01-15T12:20:00Z',
    updatedAt: '2024-01-22T12:00:00Z',
    confirmedAt: '2024-01-15T14:30:00Z',
    priceBreakdown: {
      basePrice: 650000,
      nights: 2,
      subtotal: 1300000,
      serviceFee: 130000,
      taxes: 130000,
      cleaningFee: 160000,
      discount: 0,
      total: 1720000
    },
    guestDetails: {
      adults: 12,
      children: 0,
      infants: 0
    },
    contactInfo: {
      emergencyContact: '+*************',
      estimatedArrival: '15:00'
    }
  },
  {
    id: 'booking_007',
    propertyId: 'prop_007',
    propertyName: 'Guesthouse Sosrowijayan Backpacker Hub',
    propertyImage: 'https://images.unsplash.com/photo-**********-bab0e564b8d5?w=800',
    propertyType: 'guesthouse',
    userId: 'user_007',
    userName: 'Arif Rahman',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-02-05',
    checkOut: '2024-02-07',
    guests: 1,
    totalPrice: 144000,
    status: 'cancelled',
    paymentStatus: 'refunded',
    paymentMethod: 'e_wallet',
    specialRequests: 'Business trip, butuh WiFi kencang',
    createdAt: '2024-02-01T08:15:00Z',
    updatedAt: '2024-02-03T10:30:00Z',
    cancelledAt: '2024-02-03T10:30:00Z',
    cancellationReason: 'Perubahan jadwal bisnis mendadak',
    priceBreakdown: {
      basePrice: 65000,
      nights: 2,
      subtotal: 130000,
      serviceFee: 13000,
      taxes: 13000,
      cleaningFee: 0,
      discount: 0,
      total: 144000
    },
    guestDetails: {
      adults: 1,
      children: 0,
      infants: 0
    },
    contactInfo: {
      estimatedArrival: '18:00'
    }
  },
  {
    id: 'booking_008',
    propertyId: 'prop_008',
    propertyName: 'Hotel Mewah Adisucipto Business',
    propertyImage: 'https://images.unsplash.com/photo-**********-ff40c63fe5fa?w=800',
    propertyType: 'hotel',
    userId: 'user_008',
    userName: 'Linda Wijaya',
    userEmail: '<EMAIL>',
    userPhone: '+6281234567801',
    checkIn: '2024-03-20',
    checkOut: '2024-03-22',
    guests: 2,
    totalPrice: 1650000,
    status: 'in_progress',
    paymentStatus: 'paid',
    paymentMethod: 'credit_card',
    specialRequests: 'Dokter, butuh tempat tenang untuk istirahat',
    createdAt: '2024-03-18T14:50:00Z',
    updatedAt: '2024-03-20T15:00:00Z',
    confirmedAt: '2024-03-18T16:20:00Z',
    priceBreakdown: {
      basePrice: 750000,
      nights: 2,
      subtotal: 1500000,
      serviceFee: 75000,
      taxes: 75000,
      cleaningFee: 0,
      discount: 0,
      total: 1650000
    },
    guestDetails: {
      adults: 2,
      children: 0,
      infants: 0
    },
    contactInfo: {
      emergencyContact: '+*************',
      estimatedArrival: '16:00'
    }
  },
  {
    id: 'booking_009',
    propertyId: 'prop_009',
    propertyName: 'Homestay Wirobrajan Traditional',
    propertyImage: 'https://images.unsplash.com/photo-*************-6a8506099945?w=800',
    propertyType: 'homestay',
    userId: 'user_009',
    userName: 'Fajar Nugroho',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-01-10',
    checkOut: '2024-01-13',
    guests: 1,
    totalPrice: 492000,
    status: 'completed',
    paymentStatus: 'paid',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Fotografer, butuh info spot foto tradisional',
    createdAt: '2024-01-05T11:30:00Z',
    updatedAt: '2024-01-13T11:00:00Z',
    confirmedAt: '2024-01-05T13:15:00Z',
    priceBreakdown: {
      basePrice: 120000,
      nights: 3,
      subtotal: 360000,
      serviceFee: 36000,
      taxes: 36000,
      cleaningFee: 60000,
      discount: 0,
      total: 492000
    },
    guestDetails: {
      adults: 1,
      children: 0,
      infants: 0
    },
    contactInfo: {
      estimatedArrival: '14:00'
    }
  },
  {
    id: 'booking_010',
    propertyId: 'prop_010',
    propertyName: 'Villa Sleman Modern Minimalis',
    propertyImage: 'https://images.unsplash.com/photo-*************-7fde63acd811?w=800',
    propertyType: 'villa',
    userId: 'user_010',
    userName: 'Rina Sari',
    userEmail: '<EMAIL>',
    userPhone: '+*************',
    checkIn: '2024-04-01',
    checkOut: '2024-04-03',
    guests: 3,
    totalPrice: 1320000,
    status: 'pending',
    paymentStatus: 'pending',
    paymentMethod: 'bank_transfer',
    specialRequests: 'Mahasiswa, first time ke Jogja',
    createdAt: '2024-03-25T13:20:00Z',
    updatedAt: '2024-03-25T13:20:00Z',
    priceBreakdown: {
      basePrice: 550000,
      nights: 2,
      subtotal: 1100000,
      serviceFee: 110000,
      taxes: 110000,
      cleaningFee: 0,
      discount: 0,
      total: 1320000
    },
    guestDetails: {
      adults: 3,
      children: 0,
      infants: 0
    },
    contactInfo: {
      estimatedArrival: '15:00'
    }
  }
];

// Export bookings by status
export const pendingBookings = mockBookings.filter(booking => booking.status === 'pending');
export const confirmedBookings = mockBookings.filter(booking => booking.status === 'confirmed');
export const completedBookings = mockBookings.filter(booking => booking.status === 'completed');
export const cancelledBookings = mockBookings.filter(booking => booking.status === 'cancelled');
export const inProgressBookings = mockBookings.filter(booking => booking.status === 'in_progress');

// Export bookings by payment status
export const paidBookings = mockBookings.filter(booking => booking.paymentStatus === 'paid');
export const unpaidBookings = mockBookings.filter(booking => booking.paymentStatus === 'pending');

// Export bookings by property type
export const homestayBookings = mockBookings.filter(booking => booking.propertyType === 'homestay');
export const hotelBookings = mockBookings.filter(booking => booking.propertyType === 'hotel');
export const villaBookings = mockBookings.filter(booking => booking.propertyType === 'villa');
export const guesthouseBookings = mockBookings.filter(booking => booking.propertyType === 'guesthouse');

// Export recent bookings (last 30 days)
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
export const recentBookings = mockBookings.filter(booking =>
  new Date(booking.createdAt) >= thirtyDaysAgo
);

// Export upcoming bookings
const today = new Date().toISOString().split('T')[0];
export const upcomingBookings = mockBookings.filter(booking =>
  booking.checkIn >= today && (booking.status === 'confirmed' || booking.status === 'pending')
);

// Default export
export default mockBookings;
