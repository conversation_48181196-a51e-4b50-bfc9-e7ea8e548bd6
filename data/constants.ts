// App constants and configuration values for JogjaHub

// Currency and pricing
export const CURRENCY = {
  code: 'IDR',
  symbol: 'Rp',
  name: 'Indonesian Rupiah'
};

export const PRICE_RANGES = {
  budget: { min: 50000, max: 150000, label: 'Budget (50k - 150k)' },
  mid: { min: 150000, max: 500000, label: 'Mid Range (150k - 500k)' },
  luxury: { min: 500000, max: 1000000, label: 'Luxury (500k - 1M)' }
};

// Property types
export const PROPERTY_TYPES = {
  homestay: { label: 'Homestay', icon: 'home', description: 'Rumah keluarga yang menyewakan kamar' },
  guesthouse: { label: 'Guesthouse', icon: 'building', description: 'Penginapan sederhana untuk backpacker' },
  hotel: { label: 'Hotel', icon: 'business', description: 'Hotel dengan fasilitas lengkap' },
  villa: { label: 'Villa', icon: 'villa', description: 'Villa mewah untuk keluarga besar' }
};

// Booking statuses
export const BOOKING_STATUS = {
  pending: { label: 'Menunggu Konfirmasi', color: '#FFA500', icon: 'clock' },
  confirmed: { label: 'Dikonfirmasi', color: '#4CAF50', icon: 'check-circle' },
  in_progress: { label: 'Sedang Berlangsung', color: '#2196F3', icon: 'play-circle' },
  completed: { label: 'Selesai', color: '#9E9E9E', icon: 'check-circle-outline' },
  cancelled: { label: 'Dibatalkan', color: '#F44336', icon: 'cancel' }
};

// Payment methods
export const PAYMENT_METHODS = {
  bank_transfer: { label: 'Transfer Bank', icon: 'bank', description: 'Transfer ke rekening bank' },
  credit_card: { label: 'Kartu Kredit', icon: 'credit-card', description: 'Pembayaran dengan kartu kredit' },
  e_wallet: { label: 'E-Wallet', icon: 'wallet', description: 'OVO, GoPay, Dana, dll' },
  cash: { label: 'Tunai', icon: 'cash', description: 'Bayar tunai saat check-in' }
};

// Indonesian phone number format
export const PHONE_FORMAT = {
  countryCode: '+62',
  pattern: /^(\+62|62|0)8[1-9][0-9]{6,9}$/,
  example: '+62 812-3456-7890'
};

// Yogyakarta specific data
export const YOGYAKARTA_INFO = {
  name: 'Daerah Istimewa Yogyakarta',
  shortName: 'Jogja',
  coordinates: { latitude: -7.7956, longitude: 110.3695 },
  timezone: 'Asia/Jakarta',
  currency: 'IDR',
  language: 'Indonesian, Javanese',
  area: '3,185.80 km²',
  population: '3,842,592',
  districts: [
    'Gedong Tengen', 'Kraton', 'Mergangsan', 'Gondomanan', 'Pakualaman',
    'Wirobrajan', 'Mantrijeron', 'Umbulharjo', 'Kotagede', 'Danurejan',
    'Jetis', 'Tegalrejo', 'Ngampilan', 'Bantul', 'Sleman'
  ]
};

// Cultural elements
export const CULTURAL_ELEMENTS = {
  traditional_foods: [
    'Gudeg', 'Bakpia', 'Sate Klathak', 'Wedang Ronde', 'Kopi Joss',
    'Tahu Bacem', 'Sambal Krecek', 'Nasi Kucing', 'Pecel Kembang Turi'
  ],
  traditional_arts: [
    'Batik', 'Wayang', 'Gamelan', 'Tari Klasik Jawa', 'Kerajinan Perak',
    'Kerajinan Kulit', 'Lukis Kaca', 'Ukir Kayu'
  ],
  cultural_sites: [
    'Kraton Yogyakarta', 'Taman Sari', 'Malioboro', 'Alun-alun Kidul',
    'Kotagede', 'Prambanan', 'Borobudur', 'Imogiri'
  ],
  traditional_architecture: [
    'Joglo', 'Limasan', 'Pendopo', 'Pringgitan', 'Dalem', 'Gandhok'
  ]
};

// Common Indonesian greetings and phrases
export const INDONESIAN_PHRASES = {
  greetings: {
    'Selamat datang': 'Welcome',
    'Terima kasih': 'Thank you',
    'Sama-sama': 'You\'re welcome',
    'Permisi': 'Excuse me',
    'Maaf': 'Sorry'
  },
  hospitality: {
    'Silakan masuk': 'Please come in',
    'Monggo': 'Please (Javanese)',
    'Sugeng rawuh': 'Welcome (Javanese)',
    'Matur nuwun': 'Thank you (Javanese)'
  },
  directions: {
    'Ke kiri': 'Turn left',
    'Ke kanan': 'Turn right',
    'Lurus': 'Go straight',
    'Dekat': 'Near',
    'Jauh': 'Far'
  }
};

// Rating system
export const RATING_SYSTEM = {
  scale: { min: 1, max: 5 },
  labels: {
    1: 'Sangat Buruk',
    2: 'Buruk', 
    3: 'Cukup',
    4: 'Baik',
    5: 'Sangat Baik'
  },
  categories: {
    cleanliness: 'Kebersihan',
    accuracy: 'Kesesuaian Deskripsi',
    communication: 'Komunikasi Host',
    location: 'Lokasi',
    checkIn: 'Proses Check-in',
    value: 'Nilai untuk Uang'
  }
};

// Travel types
export const TRAVEL_TYPES = {
  business: { label: 'Bisnis', icon: 'briefcase' },
  leisure: { label: 'Liburan', icon: 'beach' },
  family: { label: 'Keluarga', icon: 'family' },
  couple: { label: 'Pasangan', icon: 'heart' },
  solo: { label: 'Solo Travel', icon: 'person' },
  friends: { label: 'Teman-teman', icon: 'group' }
};

// Time formats
export const TIME_FORMATS = {
  checkIn: '14:00',
  checkOut: '12:00',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm',
  timezone: 'WIB (UTC+7)'
};

// Contact information
export const CONTACT_INFO = {
  customerService: {
    phone: '+*************',
    whatsapp: '+62 812 3456 7890',
    email: '<EMAIL>',
    hours: '08:00 - 22:00 WIB'
  },
  emergency: {
    police: '110',
    ambulance: '118',
    fire: '113',
    tourist_police: '+*************'
  }
};

// App configuration
export const APP_CONFIG = {
  name: 'JogjaHub',
  version: '1.0.0',
  description: 'Platform booking akomodasi terpercaya di Yogyakarta',
  website: 'https://jogjahub.com',
  supportedLanguages: ['id', 'en'],
  defaultLanguage: 'id',
  maxUploadSize: 5 * 1024 * 1024, // 5MB
  maxImages: 10,
  searchRadius: 50, // km
  defaultPageSize: 20
};

// Social media
export const SOCIAL_MEDIA = {
  instagram: '@jogjahub',
  facebook: 'JogjaHub Official',
  twitter: '@jogjahub',
  youtube: 'JogjaHub Channel',
  tiktok: '@jogjahub'
};

// Export all constants
export default {
  CURRENCY,
  PRICE_RANGES,
  PROPERTY_TYPES,
  BOOKING_STATUS,
  PAYMENT_METHODS,
  PHONE_FORMAT,
  YOGYAKARTA_INFO,
  CULTURAL_ELEMENTS,
  INDONESIAN_PHRASES,
  RATING_SYSTEM,
  TRAVEL_TYPES,
  TIME_FORMATS,
  CONTACT_INFO,
  APP_CONFIG,
  SOCIAL_MEDIA
};
