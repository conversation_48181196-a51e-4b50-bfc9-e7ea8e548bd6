import { Review } from '../types/review';

export const mockReviews: Review[] = [
  {
    id: 'review_001',
    bookingId: 'booking_001',
    propertyId: 'prop_001',
    propertyName: 'Homestay Malioboro Heritage',
    userId: 'user_001',
    userName: '<PERSON><PERSON>',
    userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    rating: 5,
    title: 'Pengalaman Menginap yang Luar Biasa!',
    comment: 'Homestay yang sangat nyaman dengan nuansa tradisional Jawa yang kental. Pak Budi sebagai host sangat ramah dan informatif tentang sejarah Jogja. Lokasi strategis banget, tinggal jalan kaki ke Malioboro. Sarapan gudeg dan teh hangat di pagi hari bikin betah. <PERSON><PERSON> be<PERSON>, <PERSON> dingin, WiFi kencang. Pasti balik lagi kalau ke Jogja!',
    images: [
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400',
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400'
    ],
    createdAt: '2024-02-18T10:30:00Z',
    updatedAt: '2024-02-18T10:30:00Z',
    isVerified: true,
    helpfulCount: 12,
    hostReply: {
      message: 'Terima kasih Mas Andi atas reviewnya yang sangat baik! Senang sekali bisa berbagi cerita tentang Jogja. Ditunggu kedatangannya lagi ya!',
      createdAt: '2024-02-18T14:20:00Z'
    },
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 5,
      checkIn: 5,
      value: 5
    },
    stayDuration: 2,
    travelType: 'business',
    wouldRecommend: true
  },
  {
    id: 'review_002',
    bookingId: 'booking_003',
    propertyId: 'prop_003',
    propertyName: 'Guesthouse Prawirotaman Backpacker',
    userId: 'user_003',
    userName: 'Reza Firmansyah',
    userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    rating: 4,
    title: 'Cocok untuk Backpacker Budget',
    comment: 'Guesthouse yang oke untuk backpacker seperti saya. Harga terjangkau, lokasi strategis di Prawirotaman yang memang jadi pusat backpacker. Mas Agung hostnya helpful banget, kasih info tempat makan murah dan wisata gratis. Common area nyaman buat ngobrol sama traveler lain. Cuma kamar mandinya agak kecil dan air panasnya kadang mati. Overall worth it lah untuk budget segini.',
    createdAt: '2024-03-01T09:15:00Z',
    isVerified: true,
    helpfulCount: 8,
    hostReply: {
      message: 'Makasih Mas Reza! Untuk air panas sudah kita perbaiki ya. Semoga next trip ke Jogja bisa menginap lagi di sini!',
      createdAt: '2024-03-01T16:45:00Z'
    },
    categories: {
      cleanliness: 4,
      accuracy: 4,
      communication: 5,
      location: 5,
      checkIn: 4,
      value: 5
    },
    stayDuration: 3,
    travelType: 'solo',
    wouldRecommend: true
  },
  {
    id: 'review_003',
    bookingId: 'booking_006',
    propertyId: 'prop_006',
    propertyName: 'Villa Bantul Countryside',
    userId: 'user_006',
    userName: 'Dewi Lestari',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    rating: 5,
    title: 'Perfect untuk Retreat Kelompok',
    comment: 'Villa yang sangat cocok untuk retreat guru-guru kami. Suasana pedesaan yang tenang, udara segar, pemandangan sawah yang menenangkan. Bu Retno hostnya sangat perhatian, bahkan menyediakan meeting room gratis untuk diskusi kami. Fasilitas lengkap, kolam renang bersih, dapur bisa dipakai masak bareng. Anak-anak guru pada betah banget di sini. Highly recommended untuk acara kelompok!',
    images: [
      'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400'
    ],
    createdAt: '2024-01-25T11:20:00Z',
    isVerified: true,
    helpfulCount: 15,
    hostReply: {
      message: 'Terima kasih Bu Dewi dan teman-teman guru! Senang sekali bisa membantu acara retreat yang bermanfaat. Semoga bisa bertemu lagi di lain waktu!',
      createdAt: '2024-01-25T18:30:00Z'
    },
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 4,
      checkIn: 5,
      value: 5
    },
    stayDuration: 2,
    travelType: 'friends',
    wouldRecommend: true
  },
  {
    id: 'review_004',
    bookingId: 'booking_009',
    propertyId: 'prop_009',
    propertyName: 'Homestay Wirobrajan Traditional',
    userId: 'user_009',
    userName: 'Fajar Nugroho',
    userEmail: '<EMAIL>',
    userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    rating: 5,
    title: 'Rumah Joglo Asli yang Menawan',
    comment: 'Sebagai fotografer, saya sangat terkesan dengan homestay ini. Rumah joglo asli dengan arsitektur tradisional yang masih terjaga. Bu Siti hostnya sangat ramah dan bahkan mengajari saya sedikit tentang filosofi rumah Jawa. Taman yang asri jadi spot foto yang bagus. Sarapan tradisionalnya authentic banget! Bahkan bisa belajar membatik sama Bu Siti. Pengalaman budaya yang tak terlupakan.',
    images: [
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400',
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400'
    ],
    createdAt: '2024-01-15T14:45:00Z',
    isVerified: true,
    helpfulCount: 18,
    hostReply: {
      message: 'Alhamdulillah, senang sekali Mas Fajar bisa merasakan pengalaman budaya Jawa di sini. Foto-fotonya bagus sekali! Terima kasih sudah berbagi cerita tentang homestay kami.',
      createdAt: '2024-01-15T20:15:00Z'
    },
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 4,
      checkIn: 5,
      value: 5
    },
    stayDuration: 3,
    travelType: 'solo',
    wouldRecommend: true
  },
  {
    id: 'review_005',
    bookingId: 'booking_002',
    propertyId: 'prop_002',
    propertyName: 'Villa Tugu Jogja',
    userId: 'user_002',
    userName: 'Sinta Maharani',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    rating: 5,
    title: 'Villa Mewah untuk Family Gathering',
    comment: 'Villa yang sangat bagus untuk acara keluarga besar! Kolam renangnya bersih dan aman untuk anak-anak. Bu Sari hostnya sangat membantu, bahkan menyiapkan BBQ set tanpa diminta. Kamar-kamarnya luas dan nyaman, AC dingin semua. Anak-anak senang banget main di taman yang luas. Lokasi agak jauh dari pusat kota tapi justru itu yang bikin suasana tenang dan privat. Worth every penny!',
    images: [
      'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=400',
      'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=400'
    ],
    createdAt: '2024-03-05T16:30:00Z',
    isVerified: true,
    helpfulCount: 22,
    hostReply: {
      message: 'Terima kasih Bu Sinta dan keluarga besar! Senang sekali melihat anak-anak happy bermain di villa. Keluarga Ibu selalu welcome untuk menginap lagi!',
      createdAt: '2024-03-05T19:45:00Z'
    },
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 4,
      checkIn: 5,
      value: 5
    },
    stayDuration: 2,
    travelType: 'family',
    wouldRecommend: true
  },
  {
    id: 'review_006',
    bookingId: 'booking_005',
    propertyId: 'prop_005',
    propertyName: 'Homestay Kaliurang Mountain View',
    userId: 'user_005',
    userName: 'Budi Setiawan',
    userAvatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
    rating: 4,
    title: 'Healing di Kaki Gunung Merapi',
    comment: 'Homestay yang cocok banget buat healing dan refreshing. Udara sejuk, pemandangan Gunung Merapi yang indah, suasana tenang jauh dari hiruk pikuk kota. Pak Joko hostnya ramah dan bisa jadi guide lokal untuk hiking. Sarapannya sederhana tapi enak, kopi hangatnya pas banget di pagi yang dingin. Cuma jalan menuju lokasi agak menantang, harus hati-hati kalau hujan. Overall recommended untuk yang butuh ketenangan.',
    createdAt: '2024-03-19T08:45:00Z',
    isVerified: true,
    helpfulCount: 9,
    hostReply: {
      message: 'Makasih Mas Budi! Senang bisa membantu healing trip-nya. Untuk akses jalan memang agak menantang, tapi pemandangannya worth it kan? Hehe.',
      createdAt: '2024-03-19T12:20:00Z'
    },
    categories: {
      cleanliness: 4,
      accuracy: 4,
      communication: 5,
      location: 3,
      checkIn: 4,
      value: 4
    },
    stayDuration: 2,
    travelType: 'leisure',
    wouldRecommend: true
  },
  {
    id: 'review_007',
    bookingId: 'booking_008',
    propertyId: 'prop_008',
    propertyName: 'Hotel Mewah Adisucipto Business',
    userId: 'user_008',
    userName: 'Linda Wijaya',
    userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    rating: 5,
    title: 'Hotel Bisnis Terbaik di Jogja',
    comment: 'Hotel yang sangat profesional dan nyaman untuk business traveler. Lokasi strategis dekat bandara, shuttle service yang punctual. Executive lounge-nya mewah dengan fasilitas lengkap. Kamar luas, tempat tidur empuk, WiFi super cepat. Staff sangat helpful dan responsif. Spa-nya juga bagus untuk relaksasi setelah meeting seharian. Breakfast buffet-nya variatif dengan menu internasional dan lokal. Definitely my go-to hotel kalau ke Jogja untuk bisnis.',
    createdAt: '2024-03-23T11:15:00Z',
    isVerified: true,
    helpfulCount: 14,
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 5,
      checkIn: 5,
      value: 4
    },
    stayDuration: 2,
    travelType: 'business',
    wouldRecommend: true
  },
  {
    id: 'review_008',
    bookingId: 'booking_007',
    propertyId: 'prop_007',
    propertyName: 'Guesthouse Sosrowijayan Backpacker Hub',
    userId: 'user_007',
    userName: 'Arif Rahman',
    userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    rating: 3,
    title: 'Lokasi Bagus tapi Fasilitas Perlu Upgrade',
    comment: 'Guesthouse legendaris di Sosrowijayan dengan lokasi yang sangat strategis. Dekat kemana-mana, banyak warung makan murah di sekitar. Suasana backpacker yang authentic dengan banyak traveler internasional. Tapi sayangnya fasilitas agak outdated, kamar mandi shared yang kurang bersih, WiFi lemot, AC berisik. Mas Bambang hostnya ramah sih, tapi kayaknya perlu renovasi untuk bersaing dengan guesthouse modern lainnya.',
    createdAt: '2024-02-04T09:30:00Z',
    isVerified: true,
    helpfulCount: 6,
    hostReply: {
      message: 'Terima kasih feedbacknya Mas Arif. Kami sedang dalam proses renovasi untuk meningkatkan fasilitas. Mohon maaf atas ketidaknyamanannya.',
      createdAt: '2024-02-04T15:20:00Z'
    },
    categories: {
      cleanliness: 3,
      accuracy: 4,
      communication: 4,
      location: 5,
      checkIn: 3,
      value: 3
    },
    stayDuration: 2,
    travelType: 'business',
    wouldRecommend: false
  },
  {
    id: 'review_009',
    bookingId: 'booking_001',
    propertyId: 'prop_001',
    propertyName: 'Homestay Malioboro Heritage',
    userId: 'user_004',
    userName: 'Maya Sari',
    userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    rating: 4,
    title: 'Spot Foto yang Instagramable',
    comment: 'Homestay dengan konsep tradisional yang bagus untuk konten Instagram. Arsitektur Jawa klasiknya photogenic banget, apalagi di taman belakang. Pak Budi baik banget, bahkan bantuin foto-foto dan kasih tau spot terbaik. Lokasi deket Malioboro jadi gampang explore. Cuma kamarnya agak gelap untuk foto indoor, dan WiFi kadang lemot pas upload konten. But overall, content creator pasti suka tempat ini!',
    images: [
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400'
    ],
    createdAt: '2024-01-20T13:25:00Z',
    isVerified: true,
    helpfulCount: 11,
    hostReply: {
      message: 'Terima kasih Mbak Maya! Senang bisa membantu konten Instagramnya. Untuk WiFi sudah kita upgrade ya, semoga next time lebih lancar!',
      createdAt: '2024-01-20T17:40:00Z'
    },
    categories: {
      cleanliness: 4,
      accuracy: 4,
      communication: 5,
      location: 5,
      checkIn: 4,
      value: 4
    },
    stayDuration: 1,
    travelType: 'solo',
    wouldRecommend: true
  },
  {
    id: 'review_010',
    bookingId: 'booking_004',
    propertyId: 'prop_004',
    propertyName: 'Hotel Boutique Kotagede Heritage',
    userId: 'user_010',
    userName: 'Rina Sari',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    rating: 5,
    title: 'Hotel Heritage yang Memukau',
    comment: 'Sebagai mahasiswa yang baru pertama ke Jogja, saya sangat terkesan dengan hotel ini. Arsitektur heritage-nya unik, perpaduan kolonial dan Jawa yang indah. Staff sangat ramah dan informatif tentang sejarah Kotagede. Kamar nyaman dengan sentuhan tradisional tapi fasilitas modern. Restoran fusion-nya enak banget, menu gudeg modern yang unik. Lokasi di Kotagede bikin bisa explore kerajinan perak. Meskipun agak mahal untuk kantong mahasiswa, tapi worth it untuk pengalaman yang tak terlupakan!',
    createdAt: '2024-03-15T10:20:00Z',
    isVerified: true,
    helpfulCount: 7,
    categories: {
      cleanliness: 5,
      accuracy: 5,
      communication: 5,
      location: 4,
      checkIn: 5,
      value: 4
    },
    stayDuration: 2,
    travelType: 'leisure',
    wouldRecommend: true
  }
];

// Export reviews by rating
export const excellentReviews = mockReviews.filter(review => review.rating === 5);
export const goodReviews = mockReviews.filter(review => review.rating === 4);
export const averageReviews = mockReviews.filter(review => review.rating === 3);
export const poorReviews = mockReviews.filter(review => review.rating <= 2);

// Export reviews by travel type
export const businessReviews = mockReviews.filter(review => review.travelType === 'business');
export const leisureReviews = mockReviews.filter(review => review.travelType === 'leisure');
export const familyReviews = mockReviews.filter(review => review.travelType === 'family');
export const soloReviews = mockReviews.filter(review => review.travelType === 'solo');
export const friendsReviews = mockReviews.filter(review => review.travelType === 'friends');

// Export verified reviews
export const verifiedReviews = mockReviews.filter(review => review.isVerified);

// Export reviews with images
export const reviewsWithImages = mockReviews.filter(review => review.images && review.images.length > 0);

// Export reviews with host replies
export const reviewsWithHostReplies = mockReviews.filter(review => review.hostReply);

// Export recent reviews (last 30 days)
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
export const recentReviews = mockReviews.filter(review =>
  new Date(review.createdAt) >= thirtyDaysAgo
);

// Calculate review summary
export const reviewSummary = {
  averageRating: mockReviews.reduce((sum, review) => sum + review.rating, 0) / mockReviews.length,
  totalReviews: mockReviews.length,
  ratingDistribution: {
    5: mockReviews.filter(r => r.rating === 5).length,
    4: mockReviews.filter(r => r.rating === 4).length,
    3: mockReviews.filter(r => r.rating === 3).length,
    2: mockReviews.filter(r => r.rating === 2).length,
    1: mockReviews.filter(r => r.rating === 1).length,
  },
  categoryAverages: {
    cleanliness: mockReviews.reduce((sum, r) => sum + r.categories.cleanliness, 0) / mockReviews.length,
    accuracy: mockReviews.reduce((sum, r) => sum + r.categories.accuracy, 0) / mockReviews.length,
    communication: mockReviews.reduce((sum, r) => sum + r.categories.communication, 0) / mockReviews.length,
    location: mockReviews.reduce((sum, r) => sum + r.categories.location, 0) / mockReviews.length,
    checkIn: mockReviews.reduce((sum, r) => sum + r.categories.checkIn, 0) / mockReviews.length,
    value: mockReviews.reduce((sum, r) => sum + r.categories.value, 0) / mockReviews.length,
  },
  recommendationRate: mockReviews.filter(r => r.wouldRecommend).length / mockReviews.length
};

// Default export
export default mockReviews;
