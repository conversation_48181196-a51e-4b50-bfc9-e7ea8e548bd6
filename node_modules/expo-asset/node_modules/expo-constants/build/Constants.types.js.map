{"version": 3, "file": "Constants.types.js", "sourceRoot": "", "sources": ["../src/Constants.types.ts"], "names": [], "mappings": "AAaA,MAAM,CAAN,IAAY,YAMX;AAND,WAAY,YAAY;IACtB;;;OAGG;IACH,6BAAa,CAAA;AACf,CAAC,EANW,YAAY,KAAZ,YAAY,QAMvB;AAED,eAAe;AACf,MAAM,CAAN,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC9B,qCAAa,CAAA;IACb,iDAAyB,CAAA;IACzB,mDAA2B,CAAA;AAC7B,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,QAI/B;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAN,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC5B,yCAAmB,CAAA;IACnB,uCAAiB,CAAA;IACjB,yCAAmB,CAAA;IACnB,+BAAS,CAAA;IACT,iDAA2B,CAAA;AAC7B,CAAC,EANW,kBAAkB,KAAlB,kBAAkB,QAM7B", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\nimport type {\n  EASConfig as ManifestsEASConfig,\n  ExpoGoConfig as ManifestsExpoGoConfig,\n  ExpoUpdatesManifest,\n  EmbeddedManifest,\n  ManifestAsset as ManifestAssetForReExport,\n  ManifestExtra as ManifestExtraForReExport,\n  ClientScopingConfig as ClientScopingConfigForReExport,\n  ExpoGoPackagerOpts as ExpoGoPackagerOptsForReExport,\n  // @ts-ignore -- optional interface, will gracefully degrade to `any` not installed\n} from 'expo-manifests';\n\nexport enum AppOwnership {\n  /**\n   * The experience is running inside the Expo Go app.\n   * @deprecated Use [`Constants.executionEnvironment`](#executionenvironment) instead.\n   */\n  Expo = 'expo',\n}\n\n// @docsMissing\nexport enum ExecutionEnvironment {\n  Bare = 'bare',\n  Standalone = 'standalone',\n  StoreClient = 'storeClient',\n}\n\n// @needsAudit\n/**\n * Current supported values are `handset`, `tablet`, `desktop` and `tv`. CarPlay will show up\n * as `unsupported`.\n */\nexport enum UserInterfaceIdiom {\n  Handset = 'handset',\n  Tablet = 'tablet',\n  Desktop = 'desktop',\n  TV = 'tv',\n  Unsupported = 'unsupported',\n}\n\n// @needsAudit\n/**\n * @platform ios\n */\nexport type IOSManifest = {\n  /**\n   * The build number specified in the embedded **Info.plist** value for `CFBundleVersion` in this app.\n   * In a standalone app, you can set this with the `ios.buildNumber` value in **app.json**. This\n   * may differ from the value in `Constants.expoConfig.ios.buildNumber` because the manifest\n   * can be updated, whereas this value will never change for a given native binary.\n   * The value is set to `null` in case you run your app in Expo Go.\n   */\n  buildNumber: string | null;\n  /**\n   * The Apple internal model identifier for this device.\n   * @example\n   * `iPhone1,1`\n   * @deprecated Use `expo-device`'s [`Device.modelId`](./device/#devicemodelid).\n   */\n  platform: string;\n  /**\n   * The human-readable model name of this device. For example, `\"iPhone 7 Plus\"` if it can be determined,\n   * otherwise will be `null`.\n   * @deprecated Moved to `expo-device` as [`Device.modelName`](./device/#devicemodelname).\n   */\n  model: string | null;\n  /**\n   * The user interface idiom of the current device, such as whether the app is running on an iPhone, iPad, Mac or Apple TV.\n   * @deprecated Use `expo-device`'s [`Device.getDeviceTypeAsync()`](./device/#devicegetdevicetypeasync).\n   */\n  userInterfaceIdiom: UserInterfaceIdiom;\n  /**\n   * The version of iOS running on this device.\n   * @example\n   * `10.3`\n   * @deprecated Use `expo-device`'s [`Device.osVersion`](./device/#deviceosversion).\n   */\n  systemVersion: string;\n} & Record<string, any>;\n\n// @needsAudit\n/**\n * @platform android\n */\nexport type AndroidManifest = {\n  /**\n   * The version code set by `android.versionCode` in app.json.\n   * The value is set to `null` in case you run your app in Expo Go.\n   * @deprecated Use `expo-application`'s [`Application.nativeBuildVersion`](./application/#applicationnativebuildversion).\n   */\n  versionCode: number;\n} & Record<string, any>;\n\n/**\n * @platform web\n */\nexport type WebManifest = Record<string, any>;\n\n// type re-exports to prevent breaking change\n\nexport type ManifestAsset = ManifestAssetForReExport;\nexport type Manifest = ExpoUpdatesManifest;\nexport type ManifestExtra = ManifestExtraForReExport;\nexport type EASConfig = ManifestsEASConfig;\nexport type ClientScopingConfig = ClientScopingConfigForReExport;\nexport type ExpoGoConfig = ManifestsExpoGoConfig;\nexport type ExpoGoPackagerOpts = ExpoGoPackagerOptsForReExport;\n\n// @needsAudit @docsMissing\nexport type PlatformManifest = {\n  ios?: IOSManifest;\n  android?: AndroidManifest;\n  web?: WebManifest;\n  detach?: {\n    scheme?: string;\n    [key: string]: any;\n  };\n  scheme?: string;\n  hostUri?: string;\n  developer?: string;\n} & Record<string, any>;\n\n// @needsAudit @docsMissing\nexport type NativeConstants = {\n  /**\n   * @hidden\n   */\n  name: 'ExponentConstants';\n  /**\n   * Returns `expo` when running in Expo Go, otherwise `null`.\n   * @deprecated Use [`Constants.executionEnvironment`](#executionenvironment) instead.\n   */\n  appOwnership: AppOwnership | null;\n  /**\n   * Returns `true` when the app is running in debug mode (`__DEV__`). Otherwise, returns `false`.\n   */\n  debugMode: boolean;\n  /**\n   * A human-readable name for the device type.\n   */\n  deviceName?: string;\n  /**\n   * The [device year class](https://github.com/facebook/device-year-class) of this device.\n   * @deprecated Moved to `expo-device` as [`Device.deviceYearClass`](./device/#deviceyearclass).\n   */\n  deviceYearClass: number | null;\n  /**\n   * Returns the current execution environment.\n   */\n  executionEnvironment: ExecutionEnvironment;\n  experienceUrl: string;\n  /**\n   * Nullable only on the web.\n   */\n  expoRuntimeVersion: string | null;\n  /**\n   * The version string of the Expo Go app currently running.\n   * Returns `null` in bare workflow and web.\n   */\n  expoVersion: string | null;\n  isDetached?: boolean;\n  intentUri?: string;\n  /**\n   * Returns `true` if the app is running in headless mode. Otherwise, returns `false`.\n   */\n  isHeadless: boolean;\n  linkingUri: string;\n\n  /**\n   * @hidden\n   * Manifest embedded in the build. Returns `null` when `manifest2` is non-null.\n   * @deprecated Use `Constants.expoConfig` instead, which behaves more consistently across EAS Build\n   * and EAS Update.\n   */\n  manifest: EmbeddedManifest | null;\n  /**\n   * Manifest for Expo apps using modern Expo Updates from a remote source, such as apps that\n   * use EAS Update. `Constants.expoConfig` should be used for accessing the Expo config object.\n   */\n  manifest2: ExpoUpdatesManifest | null;\n  /**\n   * The standard Expo config object defined in **app.json** and **app.config.js** files. For both\n   * classic and modern manifests, whether they are embedded or remote.\n   */\n  expoConfig:\n    | (ExpoConfig & {\n        /**\n         * Only present during development using @expo/cli.\n         */\n        hostUri?: string;\n      })\n    | null;\n  /**\n   * The standard Expo Go config object populated when running in Expo Go.\n   */\n  expoGoConfig: ManifestsExpoGoConfig | null;\n  /**\n   * The standard EAS config object populated when using EAS.\n   */\n  easConfig: ManifestsEASConfig | null;\n  /**\n   * A string that is unique to the current session of your app. It is different across apps and\n   * across multiple launches of the same app.\n   */\n  sessionId: string;\n  /**\n   * The default status bar height for the device. Does not factor in changes when location tracking\n   * is in use or a phone call is active.\n   */\n  statusBarHeight: number;\n  /**\n   * A list of the system font names available on the current device.\n   */\n  systemFonts: string[];\n  systemVersion?: number;\n  /**\n   * @hidden\n   */\n  supportedExpoSdks?: string[];\n  /**\n   * Returns the specific platform manifest object.\n   *\n   * > **Note**: This is distinct from the `manifest` and `manifest2`.\n   */\n  platform?: PlatformManifest;\n  /**\n   * Gets the user agent string which would be included in requests sent by a web view running on\n   * this device. This is probably not the same user agent you might be providing in your JS `fetch`\n   * requests.\n   */\n  getWebViewUserAgentAsync: () => Promise<string | null>;\n} & Record<string, any>;\n\n/**\n * @hidden\n */\nexport type Constants = NativeConstants & {\n  /**\n   * > **Warning**: Do not use this property. Use `manifest` by default.\n   *\n   * In certain cases accessing manifest via this property\n   * suppresses important warning about missing manifest.\n   */\n  __unsafeNoWarnManifest?: EmbeddedManifest;\n  /**\n   * > **Warning**: Do not use this property. Use `manifest2` by default.\n   *\n   * In certain cases accessing manifest via this property\n   * suppresses important warning about missing manifest.\n   */\n  __unsafeNoWarnManifest2?: ExpoUpdatesManifest;\n};\n"]}