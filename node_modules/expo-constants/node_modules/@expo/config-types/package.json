{"name": "@expo/config-types", "version": "52.0.5", "description": "Types for the Expo config object app.config.ts", "types": "build/ExpoConfig.d.ts", "main": "build/ExpoConfig.js", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "generate": "ts-node ./scripts/generate.ts", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/config-types"}, "keywords": ["json", "app.json", "app.config.js", "react-native", "expo", "react"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/config-types#readme", "files": ["build"], "devDependencies": {"expo-module-scripts": "^4.0.4", "json-schema-to-typescript": "^14.0.5", "ts-node": "^10.9.1"}, "publishConfig": {"access": "public"}, "gitHead": "50890a252047d8f4513d6859216229eee353fcb0"}