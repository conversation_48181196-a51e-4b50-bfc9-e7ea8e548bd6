{"version": 3, "file": "getAccountUsername.js", "names": ["_getUserState", "data", "require", "ANONYMOUS_USERNAME", "getAccountUsername", "manifest", "username", "owner", "process", "env", "EXPO_CLI_USERNAME", "EAS_BUILD_USERNAME", "getUserState", "read", "auth"], "sources": ["../src/getAccountUsername.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { getUserState } from './getUserState';\n\nconst ANONYMOUS_USERNAME = 'anonymous';\n\n/**\n * Get the owner of the project from the manifest if specified, falling back to a bunch of different things\n * which may or may not be the owner of the project.\n *\n * @deprecated This may not actually be the owner of the project. Prefer to fetch the project owner using\n * the EAS project ID, falling back to the `owner` field.\n */\nexport function getAccountUsername(manifest: Pick<ExpoConfig, 'owner'> = {}): string {\n  // TODO: Must match what's generated in Expo Go.\n  const username =\n    manifest.owner || process.env.EXPO_CLI_USERNAME || process.env.EAS_BUILD_USERNAME;\n  if (username) {\n    return username;\n  }\n  // Statically get the username from the global user state.\n  return getUserState().read().auth?.username || ANONYMOUS_USERNAME;\n}\n"], "mappings": ";;;;;;AAEA,SAAAA,cAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,aAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAME,kBAAkB,GAAG,WAAW;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,kBAAkBA,CAACC,QAAmC,GAAG,CAAC,CAAC,EAAU;EACnF;EACA,MAAMC,QAAQ,GACZD,QAAQ,CAACE,KAAK,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIF,OAAO,CAACC,GAAG,CAACE,kBAAkB;EACnF,IAAIL,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EACA;EACA,OAAO,IAAAM,4BAAY,EAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,IAAI,EAAER,QAAQ,IAAIH,kBAAkB;AACnE", "ignoreList": []}