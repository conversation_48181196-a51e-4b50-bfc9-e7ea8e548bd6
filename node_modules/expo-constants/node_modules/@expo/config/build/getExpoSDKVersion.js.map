{"version": 3, "file": "getExpoSDKVersion.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_resolveFrom", "_Errors", "e", "__esModule", "default", "getExpoSDKVersion", "projectRoot", "exp", "sdkVersion", "getExpoSDKVersionFromPackage", "packageJsonPath", "resolveFrom", "silent", "ConfigError", "expoPackageJson", "JsonFile", "read", "json5", "version", "packageVersion", "majorVersion", "split", "shift"], "sources": ["../src/getExpoSDKVersion.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\n\nimport { ExpoConfig } from './Config.types';\nimport { ConfigError } from './Errors';\n\n/**\n * Resolve the Expo SDK Version either from the input Expo config or from the installed\n * version of the `expo` package.\n */\nexport function getExpoSDKVersion(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'sdkVersion'> = {}\n): string {\n  return exp?.sdkVersion ?? getExpoSDKVersionFromPackage(projectRoot);\n}\n\n/**\n * Resolve the Expo SDK Version either from the input Expo config or from the installed\n * version of the `expo` package.\n */\nfunction getExpoSDKVersionFromPackage(projectRoot: string): string {\n  const packageJsonPath = resolveFrom.silent(projectRoot, 'expo/package.json');\n  if (!packageJsonPath) {\n    throw new ConfigError(\n      `Cannot determine which native SDK version your project uses because the module \\`expo\\` is not installed. Please install it with \\`yarn add expo\\` and try again.`,\n      'MODULE_NOT_FOUND'\n    );\n  }\n  const expoPackageJson = JsonFile.read(packageJsonPath, { json5: true });\n  const { version: packageVersion } = expoPackageJson;\n\n  if (!(typeof packageVersion === 'string')) {\n    // This is technically impossible.\n    throw new ConfigError(\n      `Cannot determine which native SDK version your project uses because the module \\`expo\\` has an invalid package.json (missing \\`version\\` field). Try reinstalling node modules and trying again.`,\n      'MODULE_NOT_FOUND'\n    );\n  }\n\n  const majorVersion = packageVersion.split('.').shift();\n  return `${majorVersion}.0.0`;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAC,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvC;AACA;AACA;AACA;AACO,SAASG,iBAAiBA,CAC/BC,WAAmB,EACnBC,GAAmC,GAAG,CAAC,CAAC,EAChC;EACR,OAAOA,GAAG,EAAEC,UAAU,IAAIC,4BAA4B,CAACH,WAAW,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA,SAASG,4BAA4BA,CAACH,WAAmB,EAAU;EACjE,MAAMI,eAAe,GAAGC,sBAAW,CAACC,MAAM,CAACN,WAAW,EAAE,mBAAmB,CAAC;EAC5E,IAAI,CAACI,eAAe,EAAE;IACpB,MAAM,KAAIG,qBAAW,EACnB,mKAAmK,EACnK,kBACF,CAAC;EACH;EACA,MAAMC,eAAe,GAAGC,mBAAQ,CAACC,IAAI,CAACN,eAAe,EAAE;IAAEO,KAAK,EAAE;EAAK,CAAC,CAAC;EACvE,MAAM;IAAEC,OAAO,EAAEC;EAAe,CAAC,GAAGL,eAAe;EAEnD,IAAI,EAAE,OAAOK,cAAc,KAAK,QAAQ,CAAC,EAAE;IACzC;IACA,MAAM,KAAIN,qBAAW,EACnB,kMAAkM,EAClM,kBACF,CAAC;EACH;EAEA,MAAMO,YAAY,GAAGD,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;EACtD,OAAO,GAAGF,YAAY,MAAM;AAC9B", "ignoreList": []}