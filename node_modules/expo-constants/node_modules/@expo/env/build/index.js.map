{"version": 3, "file": "index.js", "names": ["_env", "data", "require", "get", "load", "createControlledEnvironment", "exports"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { createControlledEnvironment, getFiles, isEnabled } from './env';\n\nconst { get, load } = createControlledEnvironment();\n\nexport { getFiles, get, load, isEnabled };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAMA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AANA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAM;EAAEE,GAAG;EAAEC;AAAK,CAAC,GAAG,IAAAC,kCAA2B,EAAC,CAAC;AAACC,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAAAE,OAAA,CAAAH,GAAA,GAAAA,GAAA", "ignoreList": []}