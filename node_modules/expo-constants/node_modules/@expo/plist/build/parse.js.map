{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../src/parse.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;;;kCAuBkC;;;;;;AAElC,2CAA2C;AAC3C,oDAA4B;AAE5B,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB;;;;;;;GAOG;AAEH,SAAS,gBAAgB,CAAC,IAA4B;IACpD,OAAO,CACL,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,CAC9F,CAAC;AACJ,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,WAAW,CAAC,IAA4B;IAC/C,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AAEH,SAAgB,KAAK,CAAC,GAAW;IAC/B,mDAAmD;IACnD,MAAM,GAAG,GAAG,IAAI,kBAAS,CAAC,EAAE,YAAY,KAAI,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACtE,IAAA,gBAAM,EACJ,GAAG,CAAC,eAAe,CAAC,QAAQ,KAAK,OAAO,EACxC,qDAAqD,CACtD,CAAC;IACF,IAAI,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAE/C,sDAAsD;IACtD,mCAAmC;IACnC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAEzC,OAAO,KAAK,CAAC;AACf,CAAC;AAdD,sBAcC;AAED;;;;;;GAMG;AAEH,SAAS,aAAa,CAAC,IAAyB;IAC9C,IAAI,CAAC,EAAE,OAA+B,EAAE,GAAG,EAAE,OAAc,EAAE,GAAG,EAAE,OAAO,CAAC;IAE1E,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;SACF;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,OAAO,GAAG,EAAE,CAAC;QACb,GAAG,GAAG,IAAI,CAAC;QACX,OAAO,GAAG,CAAC,CAAC;QACZ,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAAE,SAAS;YACnD,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;gBACrB,IAAA,gBAAM,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,EAAE,oCAAoC,CAAC,CAAC;gBACpF,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACL,IAAA,gBAAM,EACJ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,EACrC,kBAAkB,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,0BAA0B,CACpF,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;YACD,OAAO,IAAI,CAAC,CAAC;SACd;QACD,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,GAAG,GAAG,yBAAyB,CAAC,CAAC;SAC1E;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,OAAO,CAAC;SAChB;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,GAAG,IAAI,IAAI;oBAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACpC;SACF;QACD,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,4DAA4D;KAC7D;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;QAClC,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,EAAE,CAAC;SACX;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KACrC;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACrC,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC;SACZ;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzC,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACrC;SACF;QACD,OAAO,GAAG,CAAC;KACZ;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;QACtC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAC1D,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KACnD;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACvD,GAAG,GAAG,EAAE,CAAC;QACT,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACrC;SACF;QACD,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;KACxB;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SACnC;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC7C,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aACzD;SACF;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;KACnC;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAA,gBAAM,EAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACvD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;KAC/C;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;QACnC,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;AACH,CAAC"}