{"name": "@expo/plist", "version": "0.2.2", "description": "Mac OS X Plist parser/builder for Node.js and browsers", "main": "build/index.js", "scripts": {"build": "expo-module tsc", "prepare": "yarn run clean && yarn run build", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "watch": "yarn run build --watch --preserveWatchOutput", "prepublishOnly": "expo-module prepublishOnly"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/plist"}, "keywords": ["plist"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/plist#readme", "files": ["build"], "dependencies": {"@xmldom/xmldom": "~0.7.7", "base64-js": "^1.2.3", "xmlbuilder": "^14.0.0"}, "devDependencies": {"@types/base64-js": "^1.2.5", "expo-module-scripts": "^4.0.4"}, "publishConfig": {"access": "public"}, "gitHead": "13972cd80b8b652a2ca6a2c45e03f7a0b3915ce1"}