{"version": 3, "file": "Constants.server.js", "sourceRoot": "", "sources": ["../src/Constants.server.ts"], "names": [], "mappings": "AAKA,OAAO,EAEL,YAAY,EAEZ,oBAAoB,EAIpB,kBAAkB,GAEnB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,iBAAiB,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAEL,YAAY,EAEZ,oBAAoB,EAIpB,kBAAkB,GAEnB,CAAC;AAIF,MAAM,eAAe,GAAgB,CAAC,GAAG,EAAE;IACzC,IAAI,OAAO,iBAAiB,EAAE,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,iBAAiB,EAAE,QAAe,CAAC;AAC5C,CAAC,CAAC,EAAE,CAAC;AAEL,MAAM,SAAS,GAAc,iBAAiB,CAAC;AAE/C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE;IACjC;;;;;OAKG;IACH,sBAAsB,EAAE;QACtB,GAAG;YACD,OAAO,eAAsB,CAAC;QAChC,CAAC;QACD,UAAU,EAAE,KAAK;KAClB;IACD,QAAQ,EAAE;QACR,GAAG;YACD,OAAO,eAAsB,CAAC;QAChC,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,UAAU,EAAE;QACV,GAAG;YAQD,OAAO,eAAsB,CAAC;QAChC,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,YAAY,EAAE;QACZ,GAAG;YACD,OAAO,eAAsB,CAAC;QAChC,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,SAAS,EAAE;QACT,GAAG;YACD,OAAO,eAAsB,CAAC;QAChC,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;CACF,CAAC,CAAC;AAEH,eAAe,SAAsB,CAAC", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\nimport type { Manifest as DevLauncherManifest } from 'expo-dev-launcher';\nimport type { EmbeddedManifest, EASConfig, ExpoGoConfig } from 'expo-manifests';\nimport type { Manifest as UpdatesManifest } from 'expo-updates';\n\nimport {\n  AndroidManifest,\n  AppOwnership,\n  Constants,\n  ExecutionEnvironment,\n  IOSManifest,\n  NativeConstants,\n  PlatformManifest,\n  UserInterfaceIdiom,\n  WebManifest,\n} from './Constants.types';\nimport ExponentConstants from './ExponentConstants.web.js';\n\nexport {\n  AndroidManifest,\n  AppOwnership,\n  Constants,\n  ExecutionEnvironment,\n  IOSManifest,\n  NativeConstants,\n  PlatformManifest,\n  UserInterfaceIdiom,\n  WebManifest,\n};\n\ntype RawManifest = UpdatesManifest | DevLauncherManifest | ExpoConfig;\n\nconst PARSED_MANIFEST: RawManifest = (() => {\n  if (typeof ExponentConstants?.manifest === 'string') {\n    return JSON.parse(ExponentConstants.manifest);\n  }\n  return ExponentConstants?.manifest as any;\n})();\n\nconst constants: Constants = ExponentConstants;\n\nObject.defineProperties(constants, {\n  /**\n   * Use `manifest` property by default.\n   * This property is only used for internal purposes.\n   * It behaves similarly to the original one, but suppresses warning upon no manifest available.\n   * `expo-asset` uses it to prevent users from seeing mentioned warning.\n   */\n  __unsafeNoWarnManifest: {\n    get(): EmbeddedManifest | null {\n      return PARSED_MANIFEST as any;\n    },\n    enumerable: false,\n  },\n  manifest: {\n    get(): EmbeddedManifest | null {\n      return PARSED_MANIFEST as any;\n    },\n    enumerable: true,\n  },\n  expoConfig: {\n    get():\n      | (ExpoConfig & {\n          /**\n           * Only present during development using @expo/cli.\n           */\n          hostUri?: string;\n        })\n      | null {\n      return PARSED_MANIFEST as any;\n    },\n    enumerable: true,\n  },\n  expoGoConfig: {\n    get(): ExpoGoConfig | null {\n      return PARSED_MANIFEST as any;\n    },\n    enumerable: true,\n  },\n  easConfig: {\n    get(): EASConfig | null {\n      return PARSED_MANIFEST as any;\n    },\n    enumerable: true,\n  },\n});\n\nexport default constants as Constants;\n"]}