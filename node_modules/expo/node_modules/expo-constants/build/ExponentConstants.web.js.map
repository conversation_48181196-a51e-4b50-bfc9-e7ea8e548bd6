{"version": 3, "file": "ExponentConstants.web.js", "sourceRoot": "", "sources": ["../src/ExponentConstants.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAgC,MAAM,mBAAmB,CAAC;AAOvF,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;AAE1F,SAAS,cAAc;IACrB,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;QAChF,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,eAAe,CAAC;QACzB,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACvE,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChF,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,eAAe;IACb,IAAI,YAAY;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,oBAAoB;QACtB,OAAO,oBAAoB,CAAC,IAAI,CAAC;IACnC,CAAC;IACD,IAAI,SAAS;QACX,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,IAAI,UAAU;QACZ,IAAI,OAAO,SAAS,KAAK,WAAW;YAAE,OAAO,IAAI,CAAC;QAElD,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,WAAW;QACb,OAAQ,IAAI,CAAC,QAAiB,CAAC,UAAU,IAAI,IAAI,CAAC;IACpD,CAAC;IACD,IAAI,UAAU;QACZ,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,CAAC;YACpC,6BAA6B;YAC7B,mEAAmE;YACnE,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,cAAc,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,WAAW;QACb,+BAA+B;QAC/B,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,eAAe;QACjB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,eAAe;QACjB,wGAAwG;QACxG,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,QAAQ;QACV,gEAAgE;QAChE,8GAA8G;QAC9G,OAAO,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;IACxC,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,aAAa;QACf,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,CAAC;YACpC,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IACD,IAAI,SAAS;QACX,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,KAAK,CAAC,wBAAwB;QAC5B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;YACrC,OAAO,SAAS,CAAC,SAAS,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACiB,CAAC", "sourcesContent": ["import { ExecutionEnvironment, NativeConstants, WebManifest } from './Constants.types';\n\ndeclare let __DEV__: boolean;\ndeclare let process: { env: any };\ndeclare let navigator: Navigator;\ndeclare let location: Location;\n\nconst _sessionId = (Date.now() + '-' + Math.floor(Math.random() * 1000000000)).toString();\n\nfunction getBrowserName(): string | undefined {\n  if (typeof navigator !== 'undefined' && typeof navigator.userAgent === 'string') {\n    const agent = navigator.userAgent.toLowerCase();\n    if (agent.includes('edge')) {\n      return 'Edge';\n    } else if (agent.includes('edg')) {\n      return 'Chromium Edge';\n    } else if (agent.includes('opr') && 'opr' in window && !!window['opr']) {\n      return 'Opera';\n    } else if (agent.includes('chrome') && 'chrome' in window && !!window['chrome']) {\n      return 'Chrome';\n    } else if (agent.includes('trident')) {\n      return 'IE';\n    } else if (agent.includes('firefox')) {\n      return 'Firefox';\n    } else if (agent.includes('safari')) {\n      return 'Safari';\n    }\n  }\n\n  return undefined;\n}\n\nexport default {\n  get appOwnership() {\n    return null;\n  },\n  get executionEnvironment() {\n    return ExecutionEnvironment.Bare;\n  },\n  get sessionId(): string {\n    return _sessionId;\n  },\n  get isHeadless(): boolean {\n    if (typeof navigator === 'undefined') return true;\n\n    return /\\bHeadlessChrome\\//.test(navigator.userAgent);\n  },\n\n  get expoVersion(): string | null {\n    return (this.manifest as any)!.sdkVersion || null;\n  },\n  get linkingUri(): string {\n    if (typeof location !== 'undefined') {\n      // On native this is `exp://`\n      // On web we should use the protocol and hostname (location.origin)\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get expoRuntimeVersion(): string | null {\n    return this.expoVersion;\n  },\n  get deviceName(): string | undefined {\n    return getBrowserName();\n  },\n  get systemFonts(): string[] {\n    // TODO: Bacon: Maybe possible.\n    return [];\n  },\n  get statusBarHeight(): number {\n    return 0;\n  },\n  get deviceYearClass(): number | null {\n    // TODO: Bacon: The android version isn't very accurate either, maybe we could try and guess this value.\n    return null;\n  },\n  get manifest(): WebManifest {\n    // This is defined by @expo/webpack-config or babel-preset-expo.\n    // If your site is bundled with a different config then you may not have access to the app.json automatically.\n    return process.env.APP_MANIFEST || {};\n  },\n  get manifest2(): null {\n    return null;\n  },\n  get experienceUrl(): string {\n    if (typeof location !== 'undefined') {\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get debugMode(): boolean {\n    return __DEV__;\n  },\n  async getWebViewUserAgentAsync(): Promise<string | null> {\n    if (typeof navigator !== 'undefined') {\n      return navigator.userAgent;\n    } else {\n      return null;\n    }\n  },\n} as NativeConstants;\n"]}