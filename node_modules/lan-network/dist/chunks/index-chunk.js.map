{"version": 3, "file": "index-chunk.js", "sources": ["../../src/network.ts", "../../src/dhcp.ts", "../../src/route.ts", "../../src/index.ts"], "sourcesContent": ["import os from 'node:os';\nimport type { GatewayAssignment, NetworkAssignment } from './types';\n\nexport const DEFAULT_ASSIGNMENT: GatewayAssignment = {\n  iname: 'lo0',\n  address: '127.0.0.1',\n  netmask: '*********',\n  family: 'IPv4',\n  mac: '00:00:00:00:00:00',\n  internal: true,\n  cidr: '127.0.0.1/8',\n  gateway: null,\n};\n\nexport const parseMacStr = (macStr: string): number[] =>\n  macStr\n    .split(':')\n    .slice(0, 16)\n    .map(seq => parseInt(seq, 16));\n\nexport const parseIpStr = (ipStr: string): number => {\n  const addr = ipStr\n    .split('.')\n    .slice(0, 4)\n    .map(seq => parseInt(seq, 10));\n  return addr[3] | (addr[2] << 8) | (addr[1] << 16) | (addr[0] << 24);\n};\n\nexport const isSameSubnet = (\n  addrA: string,\n  addrB: string,\n  netmask: string\n): boolean => {\n  const rawAddrA = parseIpStr(addrA);\n  const rawAddrB = parseIpStr(addrB);\n  const rawMask = parseIpStr(netmask);\n  return (rawAddrA & rawMask) === (rawAddrB & rawMask);\n};\n\nexport const toIpStr = (addr: number): string => {\n  const MASK = (1 << 8) - 1;\n  let ipStr = '';\n  ipStr += `${((addr >>> 24) & MASK).toString(10)}.`;\n  ipStr += `${((addr >>> 16) & MASK).toString(10)}.`;\n  ipStr += `${((addr >>> 8) & MASK).toString(10)}.`;\n  ipStr += (addr & MASK).toString(10);\n  return ipStr;\n};\n\nconst getSubnetPriority = (addr: string): number => {\n  if (addr.startsWith('192.')) return 5;\n  else if (addr.startsWith('172.')) return 4;\n  else if (addr.startsWith('10.')) return 3;\n  else if (addr.startsWith('100.')) return 2;\n  else if (addr.startsWith('127.')) return 1;\n  else return 0;\n};\n\n/** Determines if an assignment is internal (indicated by the flag or by a zeroed mac address) */\nexport const isInternal = (assignment: NetworkAssignment) => {\n  if (assignment.internal) {\n    return true;\n  }\n  const mac = parseMacStr(assignment.mac);\n  if (mac.every(x => !x)) {\n    return true;\n  } else if (mac[0] === 0 && mac[1] === 21 && mac[2] === 93) {\n    // NOTE(@kitten): Microsoft virtual interface\n    return true;\n  } else if (assignment.iname.includes('vEthernet')) {\n    // NOTE(@kitten): Other Windows virtual interfaces\n    return true;\n  } else {\n    return false;\n  }\n};\n\nexport const interfaceAssignments = (): NetworkAssignment[] => {\n  const candidates: NetworkAssignment[] = [];\n  const interfaces = os.networkInterfaces();\n  for (const iname in interfaces) {\n    const assignments = interfaces[iname];\n    if (!assignments) continue;\n    for (const assignment of assignments) {\n      if (assignment.family !== 'IPv4') continue;\n      candidates.push({ ...assignment, iname });\n    }\n  }\n  return candidates.sort((a, b) => {\n    const priorityA = getSubnetPriority(a.address);\n    const priorityB = getSubnetPriority(b.address);\n    // Prioritise external interfaces, then sort by priority,\n    // when priority is equal, sort by raw IP values\n    const sortBy =\n      +isInternal(a) - +isInternal(b) ||\n      priorityB - priorityA ||\n      parseIpStr(b.address) - parseIpStr(a.address);\n    return sortBy;\n  });\n};\n\nexport const matchAssignment = (\n  candidates: NetworkAssignment[],\n  addr: string\n): GatewayAssignment | null => {\n  const rawAddr = parseIpStr(addr);\n  for (const candidate of candidates) {\n    const candidateAddr = parseIpStr(candidate.address);\n    if (rawAddr === candidateAddr) return { ...candidate, gateway: null };\n    const mask = parseIpStr(candidate.netmask);\n    if ((rawAddr & mask) === (candidateAddr & mask))\n      return { ...candidate, gateway: addr };\n  }\n  return null;\n};\n", "import { randomBytes } from 'node:crypto';\nimport { createSocket } from 'node:dgram';\nimport { parseIpStr, toIpStr, parseMacStr, isSameSubnet } from './network';\nimport type { NetworkAssignment } from './types';\n\nclass DHCPTimeoutError extends TypeError {\n  code = 'ETIMEDOUT';\n}\n\nconst computeBroadcastAddress = (assignment: NetworkAssignment) => {\n  const address = parseIpStr(assignment.address);\n  const netmask = parseIpStr(assignment.netmask);\n  return toIpStr(address | ~netmask);\n};\n\nconst dhcpDiscoverPacket = (macStr: string) => {\n  const MAC_ADDRESS = new Uint8Array(16);\n  MAC_ADDRESS.set(parseMacStr(macStr));\n  const packet = new Uint8Array(244);\n  const XID = randomBytes(4);\n  packet[0] = 1; // op = request\n  packet[1] = 1; // hw_type = ethernet\n  packet[2] = 6; // hw_len = ethernet\n  packet[3] = 0; // hops = 0\n  packet.set(XID, 4);\n  // elapsed = 0 seconds [2 bytes]\n  packet[10] = 0x80; // flags = broadcast discovery [2 bytes]\n  // client IP = null [4 bytes]\n  // own IP = null [4 bytes]\n  // server IP = null [4 bytes]\n  // gateway IP = null [4 bytes]\n  packet.set(MAC_ADDRESS, 28);\n  // sname = null [64 bytes]\n  // boot file = null [128 bytes]\n  packet.set([0x63, 0x82, 0x53, 0x63], 236); // Magic cookie\n  packet.set([0x35, 0x01, 0x01, 0xff], 240); // Trailer\n  return packet;\n};\n\nconst DHCP_TIMEOUT = 250;\nconst DHCP_CLIENT_PORT = 68;\nconst DHCP_SERVER_PORT = 67;\n\nexport const dhcpDiscover = (\n  assignment: NetworkAssignment\n): Promise<string> => {\n  return new Promise((resolve, reject) => {\n    const broadcastAddress = computeBroadcastAddress(assignment);\n    const packet = dhcpDiscoverPacket(assignment.mac);\n    const timeout = setTimeout(() => {\n      reject(\n        new DHCPTimeoutError(\n          `Received no reply to DHCPDISCOVER in ${DHCP_TIMEOUT}ms`\n        )\n      );\n    }, DHCP_TIMEOUT);\n    const socket = createSocket(\n      { type: 'udp4', reuseAddr: true },\n      (_msg, rinfo) => {\n        if (\n          !isSameSubnet(rinfo.address, assignment.address, assignment.netmask)\n        ) {\n          return;\n        }\n\n        clearTimeout(timeout);\n        resolve(rinfo.address);\n        socket.close();\n        socket.unref();\n      }\n    );\n    socket.on('error', error => {\n      clearTimeout(timeout);\n      reject(error);\n      socket.close();\n      socket.unref();\n    });\n    socket.bind(DHCP_CLIENT_PORT, () => {\n      socket.setBroadcast(true);\n      socket.setSendBufferSize(packet.length);\n      socket.send(\n        packet,\n        0,\n        packet.length,\n        DHCP_SERVER_PORT,\n        broadcastAddress,\n        error => {\n          if (error) reject(error);\n        }\n      );\n    });\n  });\n};\n", "import { createSocket } from 'dgram';\n\nconst PROBE_PORT = 53;\nconst PROBE_IP = '*******';\nconst NO_ROUTE_IP = '0.0.0.0';\n\nclass DefaultRouteError extends TypeError {\n  code = 'ECONNABORT';\n}\n\nexport const probeDefaultRoute = (): Promise<string> => {\n  return new Promise((resolve, reject) => {\n    const socket = createSocket({ type: 'udp4', reuseAddr: true });\n    socket.on('error', error => {\n      reject(error);\n      socket.close();\n      socket.unref();\n    });\n    socket.connect(PROBE_PORT, PROBE_IP, () => {\n      const address = socket.address();\n      if (address && 'address' in address && address.address !== NO_ROUTE_IP) {\n        resolve(address.address);\n      } else {\n        reject(new DefaultRouteError('No route to host'));\n      }\n      socket.close();\n      socket.unref();\n    });\n  });\n};\n", "import { spawnSync } from 'child_process';\nimport { dhcpDiscover } from './dhcp';\nimport { probeDefaultRoute } from './route';\nimport {\n  DEFAULT_ASSIGNMENT,\n  interfaceAssignments,\n  matchAssignment,\n  isInternal,\n} from './network';\nimport type { GatewayAssignment } from './types';\n\nexport async function lanNetwork(): Promise<GatewayAssignment> {\n  // Get IPv4 network assignments, sorted by:\n  // - external first\n  // - LAN-reserved IP range priority\n  // - address value\n  const assignments = interfaceAssignments();\n  if (!assignments.length) {\n    // If we have no assignments (which shouldn't ever happen, we make up a loopback interface)\n    return DEFAULT_ASSIGNMENT;\n  }\n\n  let assignment: GatewayAssignment | null;\n\n  // First, we attempt to probe the default route to a publicly routed IP\n  // This will generally fail if there's no route, e.g. if the network is offline\n  try {\n    const defaultRoute = await probeDefaultRoute();\n    // If this route matches a known assignment, return it without a gateway\n    if (\n      (assignment = matchAssignment(assignments, defaultRoute)) &&\n      !isInternal(assignment)\n    ) {\n      return assignment;\n    }\n  } catch {\n    // Ignore errors, since we have a fallback method\n  }\n\n  // Second, attempt to discover a gateway's DHCP network\n  // Because without a gateway we won't get a reply, we do this in parallel\n  const discoveries = await Promise.allSettled(\n    assignments.map(assignment => {\n      // For each assignment, we send a DHCPDISCOVER packet to its network mask\n      return dhcpDiscover(assignment);\n    })\n  );\n  for (const discovery of discoveries) {\n    // The first discovered gateway is returned, if it matches an assignment\n    if (discovery.status === 'fulfilled' && discovery.value) {\n      const dhcpRoute = discovery.value;\n      if ((assignment = matchAssignment(assignments, dhcpRoute))) {\n        return assignment;\n      }\n    }\n  }\n\n  // As a fallback, we choose the first assignment, since they're ordered by likely candidates\n  // This may return 127.0.0.1, typically as a last resort\n  return { ...assignments[0], gateway: null };\n}\n\nexport function lanNetworkSync(): GatewayAssignment {\n  const subprocessPath = require.resolve('lan-network/subprocess');\n  const { error, status, stdout } = spawnSync(\n    process.execPath,\n    [subprocessPath],\n    {\n      shell: false,\n      timeout: 500,\n      encoding: 'utf8',\n      windowsVerbatimArguments: false,\n      windowsHide: true,\n    }\n  );\n  if (status || error) {\n    return DEFAULT_ASSIGNMENT;\n  } else if (!status && typeof stdout === 'string') {\n    const json = JSON.parse(stdout.trim()) as GatewayAssignment;\n    return typeof json === 'object' && json && 'address' in json\n      ? json\n      : DEFAULT_ASSIGNMENT;\n  } else {\n    return DEFAULT_ASSIGNMENT;\n  }\n}\n"], "names": ["DEFAULT_ASSIGNMENT", "iname", "address", "netmask", "family", "mac", "internal", "cidr", "gateway", "parseMacStr", "macStr", "split", "slice", "map", "seq", "parseInt", "parseIpStr", "ipStr", "addr", "getSubnetPriority", "startsWith", "isInternal", "assignment", "every", "x", "includes", "interfaceAssignments", "candidates", "interfaces", "os", "networkInterfaces", "assignments", "push", "sort", "a", "b", "priorityA", "priorityB", "matchAssignment", "rawAddr", "candidate", "candidate<PERSON><PERSON><PERSON>", "mask", "DHCPTimeoutError", "TypeError", "code", "dhcpDiscover", "Promise", "resolve", "reject", "broadcastAddress", "MASK", "toString", "toIpStr", "computeBroadcastAddress", "packet", "MAC_ADDRESS", "Uint8Array", "set", "XID", "randomBytes", "dhcpDiscoverPacket", "timeout", "setTimeout", "socket", "createSocket", "type", "reuseAddr", "_msg", "rinfo", "addrA", "addrB", "rawAddrA", "rawAddrB", "rawMask", "isSameSubnet", "clearTimeout", "close", "unref", "on", "error", "bind", "setBroadcast", "setSendBufferSize", "length", "send", "DefaultRouteError", "probeDefaultRoute", "connect", "async", "lanNetwork", "defaultRoute", "discoveries", "allSettled", "discovery", "status", "value", "lanNetworkSync", "subprocessPath", "require", "stdout", "spawnSync", "process", "execPath", "shell", "encoding", "windowsVerbatimArguments", "windowsHide", "json", "JSON", "parse", "trim"], "mappings": ";;;;;;;;;;AAGO,IAAMA,IAAwC;EACnDC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRC,KAAK;EACLC,WAAU;EACVC,MAAM;EACNC,SAAS;;;AAGJ,IAAMC,cAAeC,KAC1BA,EACGC,MAAM,KACNC,MAAM,GAAG,IACTC,KAAIC,KAAOC,SAASD,GAAK;;AAEvB,IAAME,aAAcC;EACzB,IAAMC,IAAOD,EACVN,MAAM,KACNC,MAAM,GAAG,GACTC,KAAIC,KAAOC,SAASD,GAAK;EAC5B,OAAOI,EAAK,KAAMA,EAAK,MAAM,IAAMA,EAAK,MAAM,KAAOA,EAAK,MAAM;AAAG;;AAwBrE,IAAMC,oBAAqBD;EACzB,IAAIA,EAAKE,WAAW;IAAS,OAAO;SAC/B,IAAIF,EAAKE,WAAW;IAAS,OAAO;SACpC,IAAIF,EAAKE,WAAW;IAAQ,OAAO;SACnC,IAAIF,EAAKE,WAAW;IAAS,OAAO;SACpC,IAAIF,EAAKE,WAAW;IAAS,OAAO;;IACpC,OAAO;;AAAC;;AAIR,IAAMC,aAAcC;EACzB,IAAIA,EAAWhB;IACb,QAAO;;EAET,IAAMD,IAAMI,YAAYa,EAAWjB;EACnC,IAAIA,EAAIkB,OAAMC,MAAMA;IAClB,QAAO;SACF,IAAe,MAAXnB,EAAI,MAAuB,OAAXA,EAAI,MAAwB,OAAXA,EAAI;IAE9C,QAAO;SACF,IAAIiB,EAAWrB,MAAMwB,SAAS;IAEnC,QAAO;;IAEP,QAAO;;AACT;;AAGWC,IAAAA,uBAAuBA;EAClC,IAAMC,IAAkC;EACxC,IAAMC,IAAaC,EAAGC;EACtB,KAAK,IAAM7B,KAAS2B,GAAY;IAC9B,IAAMG,IAAcH,EAAW3B;IAC/B,KAAK8B;MAAa;;IAClB,KAAK,IAAMT,KAAcS,GAAa;MACpC,IAA0B,WAAtBT,EAAWlB;QAAmB;;MAClCuB,EAAWK,KAAK;WAAKV;QAAYrB;;AACnC;AACF;EACA,OAAO0B,EAAWM,MAAK,CAACC,GAAGC;IACzB,IAAMC,IAAYjB,kBAAkBe,EAAEhC;IACtC,IAAMmC,IAAYlB,kBAAkBgB,EAAEjC;IAOtC,QAHGmB,WAAWa,MAAMb,WAAWc,MAC7BE,IAAYD,KACZpB,WAAWmB,EAAEjC,WAAWc,WAAWkB,EAAEhC;AAC1B;AACb;;IAGSoC,kBAAkBA,CAC7BX,GACAT;EAEA,IAAMqB,IAAUvB,WAAWE;EAC3B,KAAK,IAAMsB,KAAab,GAAY;IAClC,IAAMc,IAAgBzB,WAAWwB,EAAUtC;IAC3C,IAAIqC,MAAYE;MAAe,OAAO;WAAKD;QAAWhC,SAAS;;;IAC/D,IAAMkC,IAAO1B,WAAWwB,EAAUrC;IAClC,KAAKoC,IAAUG,OAAWD,IAAgBC;MACxC,OAAO;WAAKF;QAAWhC,SAASU;;;AACpC;EACA,OAAO;AAAI;;AC5Gb,MAAMyB,yBAAyBC;EAC7BC,KAAO;;;AAqCIC,IAAAA,eACXxB,KAEO,IAAIyB,SAAQ,CAACC,GAASC;EAC3B,IAAMC,IAtCuB5B,MD8BTJ;IACtB,IAAMiC,IAAO;IACb,IAAIlC,IAAQ;IACZA,KAAS,IAAKC,MAAS,KAAMiC,GAAMC,SAAS;IAC5CnC,KAAS,IAAKC,MAAS,KAAMiC,GAAMC,SAAS;IAG5C,QAFAnC,KAAS,IAAKC,MAAS,IAAKiC,GAAMC,SAAS,WACjClC,IAAOiC,GAAMC,SAAS;AACpB,IClCLC,CAFSrC,WAAWM,EAAWpB,YACtBc,WAAWM,EAAWnB,UAoCXmD,CAAwBhC;EACjD,IAAMiC,IAjCkB7C;IAC1B,IAAM8C,IAAc,IAAIC,WAAW;IACnCD,EAAYE,IAAIjD,YAAYC;IAC5B,IAAM6C,IAAS,IAAIE,WAAW;IAC9B,IAAME,IAAMC,EAAWA,YAAC;IACxBL,EAAO,KAAK;IACZA,EAAO,KAAK;IACZA,EAAO,KAAK;IACZA,EAAO,KAAK;IACZA,EAAOG,IAAIC,GAAK;IAEhBJ,EAAO,MAAM;IAKbA,EAAOG,IAAIF,GAAa;IAGxBD,EAAOG,IAAI,EAAC,IAAM,KAAM,IAAM,MAAO;IACrCH,EAAOG,IAAI,EAAC,IAAM,GAAM,GAAM,OAAO;IACrC,OAAOH;AAAM,IAYIM,CAAmBvC,EAAWjB;EAC7C,IAAMyD,IAAUC,YAAW;IACzBd,EACE,IAAIN,iBACF;AAEH,MAfc;EAiBjB,IAAMqB,IAASC,EAAAA,aACb;IAAEC,MAAM;IAAQC,YAAW;MAC3B,CAACC,GAAMC;IACL,MD9BNC,IC+BsBD,EAAMnE,SD9B5BqE,IC8BqCjD,EAAWpB,SD7BhDC,IC6ByDmB,EAAWnB,SD3B9DqE,IAAWxD,WAAWsD,IACtBG,IAAWzD,WAAWuD;IACtBG,IAAU1D,WAAWb,KACnBqE,IAAWE,OAAcD,IAAWC;MC0BpC;;IDlCkBC,IAC1BL,GACAC,GACApE,GAEMqE,GACAC,GACAC;IC8BAE,aAAad;IACbd,EAAQqB,EAAMnE;IACd8D,EAAOa;IACPb,EAAOc;AAAO;EAGlBd,EAAOe,GAAG,UAASC;IACjBJ,aAAad;IACbb,EAAO+B;IACPhB,EAAOa;IACPb,EAAOc;AAAO;EAEhBd,EAAOiB,KArCc,KAqCS;IAC5BjB,EAAOkB,cAAa;IACpBlB,EAAOmB,kBAAkB5B,EAAO6B;IAChCpB,EAAOqB,KACL9B,GACA,GACAA,EAAO6B,QA1CU,IA4CjBlC,IACA8B;MACE,IAAIA;QAAO/B,EAAO+B;;AAAM;AAE3B;AACD;;ACpFN,MAAMM,0BAA0B1C;EAC9BC,KAAO;;;AAGI0C,IAAAA,oBAAoBA,MACxB,IAAIxC,SAAQ,CAACC,GAASC;EAC3B,IAAMe,IAASC,EAAAA,aAAa;IAAEC,MAAM;IAAQC,YAAW;;EACvDH,EAAOe,GAAG,UAASC;IACjB/B,EAAO+B;IACPhB,EAAOa;IACPb,EAAOc;AAAO;EAEhBd,EAAOwB,QAhBQ,IACF,YAewB;IACnC,IAAMtF,IAAU8D,EAAO9D;IACvB,IAAIA,KAAW,aAAaA,KAhBd,cAgByBA,EAAQA;MAC7C8C,EAAQ9C,EAAQA;;MAEhB+C,EAAO,IAAIqC,kBAAkB;;IAE/BtB,EAAOa;IACPb,EAAOc;AAAO;AACd;;;;;;;;qBChBCW,eAAeC;EAKpB,IAAM3D,IAAcL;EACpB,KAAKK,EAAYqD;IAEf,OAAOpF;;EAGT,IAAIsB;EAIJ;IACE,IAAMqE,UAAqBJ;IAE3B,KACGjE,IAAagB,gBAAgBP,GAAa4D,QAC1CtE,WAAWC;MAEZ,OAAOA;;AAEX,IAAE,OACA;EAKF,IAAMsE,UAAoB7C,QAAQ8C,WAChC9D,EAAYlB,KAAIS,KAEPwB,aAAaxB;EAGxB,KAAK,IAAMwE,KAAaF;IAEtB,IAAyB,gBAArBE,EAAUC,UAA0BD,EAAUE,OAAO;MAEvD,IAAK1E,IAAagB,gBAAgBP,GADhB+D,EAAUE;QAE1B,OAAO1E;;AAEX;;EAKF,OAAO;OAAKS,EAAY;IAAIvB,SAAS;;AACvC;;yBAEO,SAASyF;EACd,IAAMC,IAAiBC,QAAQnD,QAAQ;EACvC,KAAMgC,OAAEA,GAAKe,QAAEA,GAAMK,QAAEA,KAAWC,EAASA,UACzCC,QAAQC,UACR,EAACL,KACD;IACEM,QAAO;IACP1C,SAAS;IACT2C,UAAU;IACVC,2BAA0B;IAC1BC,cAAa;;EAGjB,IAAIZ,KAAUf;IACZ,OAAOhF;SACF,KAAK+F,KAA4B,mBAAXK,GAAqB;IAChD,IAAMQ,IAAOC,KAAKC,MAAMV,EAAOW;IAC/B,OAAuB,mBAATH,KAAqBA,KAAQ,aAAaA,IACpDA,IACA5G;AACN;IACE,OAAOA;;AAEX;;;;"}