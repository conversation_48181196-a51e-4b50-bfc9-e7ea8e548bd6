/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.common.build;

import com.facebook.react.BuildConfig;

/**
 * Convenience class for accessing auto-generated BuildConfig so that a) other modules can just
 * depend on this module instead of having to manually depend on generating their own build config
 * and b) we don't have to deal with IntelliJ getting confused about the autogenerated BuildConfig
 * class all over the place.
 */
public class ReactBuildConfig {

  public static final boolean DEBUG = BuildConfig.DEBUG;
  public static final boolean IS_INTERNAL_BUILD = BuildConfig.IS_INTERNAL_BUILD;
  public static final int EXOPACKAGE_FLAGS = BuildConfig.EXOPACKAGE_FLAGS;
  public static final int HERMES_BYTECODE_VERSION = BuildConfig.HERMES_BYTECODE_VERSION;
}
