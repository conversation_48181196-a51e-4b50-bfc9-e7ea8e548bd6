load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "fb_xplat_cxx_test",
    "react_native_xplat_target",
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["*.cpp"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
    ],
    contacts = ["<EMAIL>"],
    platforms = (ANDROID, APPLE, CXX),
    visibility = [
        react_native_xplat_target("cxxreact/..."),
    ],
    deps = [
        "//xplat/folly:init_init",
        "//xplat/third-party/gmock:gtest",
        react_native_xplat_target("cxxreact:bridge"),
        react_native_xplat_target("cxxreact:jsbigstring"),
    ],
)
