#!/bin/bash

set -e

DIR=$(cd -P "$(dirname "$(readlink "${BASH_SOURCE[0]}" || echo "${BASH_SOURCE[0]}")")" && pwd)

MSGTYPES_PATH="${DIR}/message_types.txt"
HEADER_PATH="${DIR}/../chrome/MessageTypes.h"
CPP_PATH="${DIR}/../chrome/MessageTypes.cpp"

FBSOURCE=$(hg root)
CLANG_FORMAT="${FBSOURCE}/tools/third-party/clang-format/clang-format"
SIGNEDSOURCE="${FBSOURCE}/tools/signedsource"

pushd "../../../../packages/hermes-inspector-msggen"

yarn install
yarn build

node bin/index.js \
  --ignore-experimental \
  --include-experimental=Runtime.getHeapUsage \
  --roots "${MSGTYPES_PATH}" \
  "${HEADER_PATH}" "${CPP_PATH}"

"${CLANG_FORMAT}" -i --style=file "${HEADER_PATH}"
"${CLANG_FORMAT}" -i --style=file "${CPP_PATH}"

"${SIGNEDSOURCE}" sign "${HEADER_PATH}"
"${SIGNEDSOURCE}" sign "${CPP_PATH}"

popd >/dev/null
