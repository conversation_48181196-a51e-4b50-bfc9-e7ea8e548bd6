load("//tools/build_defs/oss:rn_defs.bzl", "react_native_xplat_dep", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "jsi",
    srcs = [
        "jsi/jsi.cpp",
    ],
    header_namespace = "",
    exported_headers = [
        "jsi/instrumentation.h",
        "jsi/jsi.h",
        "jsi/jsi-inl.h",
        "jsi/jsilib.h",
    ],
    compiler_flags = [
        "-O3",
        "-Wextra",
        "-Wcast-qual",
        "-Wdelete-non-virtual-dtor",
        "-Wwrite-strings",
    ],
    compiler_flags_pedantic = True,
    cxx_compiler_flags = [
        "-Wglobal-constructors",
        "-Wmissing-prototypes",
    ],
    fbobjc_compiler_flags = [
        "-Wglobal-constructors",
        "-Wmissing-prototypes",
    ],
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    visibility = ["PUBLIC"],
)

rn_xplat_cxx_library(
    name = "JSIDynamic",
    srcs = [
        "jsi/JSIDynamic.cpp",
    ],
    header_namespace = "",
    exported_headers = [
        "jsi/JSIDynamic.h",
    ],
    compiler_flags_pedantic = True,
    fbobjc_force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    visibility = [
        "PUBLIC",
    ],
    xcode_public_headers_symlinks = True,
    deps = [
        react_native_xplat_dep("jsi:jsi"),
    ],
)
