load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "reactperflogger",
    srcs = glob(["**/*.cpp"]),
    header_namespace = "",
    exported_headers = {
        "reactperflogger/BridgeNativeModulePerfLogger.h": "reactperflogger/BridgeNativeModulePerfLogger.h",
        "reactperflogger/NativeModulePerfLogger.h": "reactperflogger/NativeModulePerfLogger.h",
    },
    compiler_flags = [
        "-Wno-global-constructors",
    ],
    compiler_flags_pedantic = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preferred_linkage = "static",
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
)
