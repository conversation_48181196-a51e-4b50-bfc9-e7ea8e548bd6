{"version": 3, "file": "opts-arg.js", "sourceRoot": "", "sources": ["../../../src/opts-arg.ts"], "names": [], "mappings": ";;;AAAA,2BAOW;AAwDJ,MAAM,OAAO,GAAG,CAAC,IAAoB,EAAyB,EAAE;IACrE,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;KACvB;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,IAAI,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAA;KAChC;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;KACtB;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,IAAI,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAA;KACnC;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAA;KAChD;IAED,MAAM,QAAQ,GAAG,IAA6B,CAAA;IAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,EAAE,CAAA;IAE5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,UAAK,CAAA;IAEhD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;QAC/B,CAAC,CAAC,IAAI,CAAC,UAAU;QACjB,CAAC,CAAC,KAAK,EACH,IAAY,EACZ,OAAuD,EAC1B,EAAE;YAC/B,OAAO,IAAI,OAAO,CAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAClD,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CACzC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CACzB,CACF,CAAA;QACH,CAAC,CAAA;IAEL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,SAAI,CAAA;IAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;QAC7B,CAAC,CAAC,IAAI,CAAC,SAAS;QAChB,CAAC,CAAC,KAAK,EAAE,IAAY,EAAE,EAAE,CACrB,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,CAAA;IAEP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,aAAQ,CAAA;IAC5D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,cAAS,CAAA;IAEhE,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAA;AA3CY,QAAA,OAAO,WA2CnB", "sourcesContent": ["import {\n  MakeDirectoryOptions,\n  mkdir,\n  mkdirSync,\n  stat,\n  Stats,\n  statSync,\n} from 'fs'\n\nexport interface FsProvider {\n  stat?: (\n    path: string,\n    callback: (err: NodeJS.ErrnoException | null, stats: Stats) => any\n  ) => any\n  mkdir?: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean },\n    callback: (err: NodeJS.ErrnoException | null, made?: string) => any\n  ) => any\n  statSync?: (path: string) => Stats\n  mkdirSync?: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean }\n  ) => string | undefined\n}\n\ninterface Options extends FsProvider {\n  mode?: number | string\n  fs?: FsProvider\n  mkdirAsync?: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean }\n  ) => Promise<string | undefined>\n  statAsync?: (path: string) => Promise<Stats>\n}\n\nexport type MkdirpOptions = Options | number | string\n\nexport interface MkdirpOptionsResolved {\n  mode: number\n  fs: FsProvider\n  mkdirAsync: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean }\n  ) => Promise<string | undefined>\n  statAsync: (path: string) => Promise<Stats>\n  stat: (\n    path: string,\n    callback: (err: NodeJS.ErrnoException | null, stats: Stats) => any\n  ) => any\n  mkdir: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean },\n    callback: (err: NodeJS.ErrnoException | null, made?: string) => any\n  ) => any\n  statSync: (path: string) => Stats\n  mkdirSync: (\n    path: string,\n    opts: MakeDirectoryOptions & { recursive?: boolean }\n  ) => string | undefined\n  recursive?: boolean\n}\n\nexport const optsArg = (opts?: MkdirpOptions): MkdirpOptionsResolved => {\n  if (!opts) {\n    opts = { mode: 0o777 }\n  } else if (typeof opts === 'object') {\n    opts = { mode: 0o777, ...opts }\n  } else if (typeof opts === 'number') {\n    opts = { mode: opts }\n  } else if (typeof opts === 'string') {\n    opts = { mode: parseInt(opts, 8) }\n  } else {\n    throw new TypeError('invalid options argument')\n  }\n\n  const resolved = opts as MkdirpOptionsResolved\n  const optsFs = opts.fs || {}\n\n  opts.mkdir = opts.mkdir || optsFs.mkdir || mkdir\n\n  opts.mkdirAsync = opts.mkdirAsync\n    ? opts.mkdirAsync\n    : async (\n        path: string,\n        options: MakeDirectoryOptions & { recursive?: boolean }\n      ): Promise<string | undefined> => {\n        return new Promise<string | undefined>((res, rej) =>\n          resolved.mkdir(path, options, (er, made) =>\n            er ? rej(er) : res(made)\n          )\n        )\n      }\n\n  opts.stat = opts.stat || optsFs.stat || stat\n  opts.statAsync = opts.statAsync\n    ? opts.statAsync\n    : async (path: string) =>\n        new Promise((res, rej) =>\n          resolved.stat(path, (err, stats) => (err ? rej(err) : res(stats)))\n        )\n\n  opts.statSync = opts.statSync || optsFs.statSync || statSync\n  opts.mkdirSync = opts.mkdirSync || optsFs.mkdirSync || mkdirSync\n\n  return resolved\n}\n"]}