{"name": "jog<PERSON><PERSON>", "version": "1.0.0", "description": "JogjaHub - Platform Booking Akomodasi Yogyakarta", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "validate": "node scripts/expoValidate.js", "preflight": "node scripts/expoValidate.js", "install-deps": "bash scripts/install-deps.sh", "setup": "npm run install-deps", "test": "jest", "build": "expo build", "eject": "expo eject", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\""}, "keywords": ["mock-data", "dummy-data", "jog<PERSON><PERSON>", "yogyakarta", "accommodation", "booking", "indonesia", "mvp", "testing"], "author": "JogjaHub Team", "license": "MIT", "dependencies": {"expo": "~53.0.0", "expo-status-bar": "~2.0.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.10.5", "react-native-screens": "~3.31.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@react-navigation/bottom-tabs": "^6.6.1", "react-native-vector-icons": "^10.2.0", "react-native-maps": "1.18.0", "expo-location": "~18.0.0", "expo-image-picker": "~16.0.0", "expo-constants": "~17.0.0", "@expo/vector-icons": "^14.0.0"}, "devDependencies": {"babel-plugin-module-resolver": "^5.0.0", "@babel/core": "^7.25.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "jest": "^29.7.0", "prettier": "^3.3.0", "typescript": "^5.6.0"}, "repository": {"type": "git", "url": "https://github.com/jogjahub/mock-data.git"}, "bugs": {"url": "https://github.com/jogjahub/mock-data/issues"}, "homepage": "https://github.com/jogjahub/mock-data#readme"}