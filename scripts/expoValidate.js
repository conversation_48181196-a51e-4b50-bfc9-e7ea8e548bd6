#!/usr/bin/env node

// Simple validation script that can run in Node.js environment
// This is a simplified version for quick validation before running Expo

const fs = require('fs');
const path = require('path');

console.log('🏨 JogjaHub Expo SDK 53 Pre-flight Validation');
console.log('==============================================\n');

// Check if required files exist
const requiredFiles = [
  'App.tsx',
  'app.json',
  'package.json',
  'tsconfig.json',
  'babel.config.js',
  'metro.config.js',
  'data/index.ts',
  'data/properties.ts',
  'data/users.ts',
  'data/bookings.ts',
  'data/reviews.ts',
  'types/index.ts',
  'components/DataValidation.tsx',
  'utils/expoValidation.ts'
];

let allFilesExist = true;

console.log('📁 Checking required files...');
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

console.log('');

// Check package.json dependencies
console.log('📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredDeps = [
    'expo',
    'react',
    'react-native',
    '@react-navigation/native',
    '@react-navigation/bottom-tabs',
    '@expo/vector-icons'
  ];

  // Check SDK version
  if (packageJson.dependencies && packageJson.dependencies.expo) {
    const expoVersion = packageJson.dependencies.expo;
    if (expoVersion.includes('53')) {
      console.log(`✅ Expo SDK 53 detected: ${expoVersion}`);
    } else {
      console.log(`⚠️  Expo version: ${expoVersion} - Should be SDK 53 for latest Expo Go compatibility`);
    }
  }

  let allDepsPresent = true;
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - MISSING`);
      allDepsPresent = false;
    }
  });

  if (!allDepsPresent) {
    console.log('\n⚠️  Some dependencies are missing. Run: npm install');
  }

} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  allFilesExist = false;
}

console.log('');

// Check data files structure
console.log('📊 Checking data files...');
try {
  // Simple check for data files existence and basic structure
  const dataFiles = [
    'data/properties.ts',
    'data/users.ts', 
    'data/bookings.ts',
    'data/reviews.ts'
  ];

  dataFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Basic content checks
      if (file.includes('properties') && content.includes('export') && content.includes('Property')) {
        console.log(`✅ ${file} - Contains property data`);
      } else if (file.includes('users') && content.includes('export') && content.includes('User')) {
        console.log(`✅ ${file} - Contains user data`);
      } else if (file.includes('bookings') && content.includes('export') && content.includes('Booking')) {
        console.log(`✅ ${file} - Contains booking data`);
      } else if (file.includes('reviews') && content.includes('export') && content.includes('Review')) {
        console.log(`✅ ${file} - Contains review data`);
      } else {
        console.log(`⚠️  ${file} - File exists but structure unclear`);
      }
    } else {
      console.log(`❌ ${file} - MISSING`);
    }
  });

} catch (error) {
  console.log('❌ Error checking data files:', error.message);
}

console.log('');

// Final summary
if (allFilesExist) {
  console.log('🎉 Pre-flight validation passed!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Update Expo Go app to latest version (SDK 53 support)');
  console.log('2. Run: npm install (if not done already)');
  console.log('3. Run: npm start (to start Expo development server)');
  console.log('4. Use Expo Go app to scan QR code');
  console.log('5. Check the Validation tab in the app for detailed data validation');
  console.log('');
  console.log('🚀 Ready to run JogjaHub with Expo SDK 53!');
  process.exit(0);
} else {
  console.log('💥 Pre-flight validation failed!');
  console.log('');
  console.log('Please ensure all required files are present before running Expo.');
  console.log('Check the missing files listed above.');
  process.exit(1);
}
