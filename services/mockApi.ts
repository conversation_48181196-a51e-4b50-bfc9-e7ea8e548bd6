import { ApiResponse, PaginatedResponse, SearchResponse, MockApiConfig } from '../types/api';
import { Property, PropertyFilters, PropertySearchParams } from '../types/property';
import { User } from '../types/auth';
import { Booking, BookingRequest, BookingFilters } from '../types/booking';
import { Review, ReviewRequest, ReviewFilters } from '../types/review';

// Import mock data
import { allProperties } from '../data/properties';
import mockUsers from '../data/users';
import mockBookings from '../data/bookings';
import mockReviews from '../data/reviews';

// Mock API configuration
const mockConfig: MockApiConfig = {
  baseUrl: 'https://api.jogjahub.com/v1',
  timeout: 5000,
  retryAttempts: 3,
  simulateNetworkDelay: true,
  networkDelayRange: [200, 800],
  errorRate: 0.05 // 5% chance of random errors
};

// Utility function to simulate network delay
const simulateDelay = async (): Promise<void> => {
  if (!mockConfig.simulateNetworkDelay) return;
  
  const [min, max] = mockConfig.networkDelayRange;
  const delay = Math.random() * (max - min) + min;
  await new Promise(resolve => setTimeout(resolve, delay));
};

// Utility function to simulate random errors
const simulateError = (): void => {
  if (Math.random() < mockConfig.errorRate) {
    throw new Error('Simulated network error');
  }
};

// Utility function to paginate results
const paginate = <T>(data: T[], page: number = 1, limit: number = 20): PaginatedResponse<T> => {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = data.slice(startIndex, endIndex);
  
  return {
    data: paginatedData,
    meta: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
      hasNext: endIndex < data.length,
      hasPrev: page > 1
    }
  };
};

// Property Service
export const propertyService = {
  // Get all properties with filters
  getProperties: async (filters: PropertyFilters = {}): Promise<ApiResponse<Property[]>> => {
    await simulateDelay();
    simulateError();

    let filteredProperties = [...allProperties];

    // Apply filters
    if (filters.type && filters.type.length > 0) {
      filteredProperties = filteredProperties.filter(p => filters.type!.includes(p.type));
    }

    if (filters.priceRange) {
      const [min, max] = filters.priceRange;
      filteredProperties = filteredProperties.filter(p => p.price >= min && p.price <= max);
    }

    if (filters.amenities && filters.amenities.length > 0) {
      filteredProperties = filteredProperties.filter(p => 
        filters.amenities!.some(amenity => p.amenities.includes(amenity))
      );
    }

    if (filters.district) {
      filteredProperties = filteredProperties.filter(p => 
        p.location.district.toLowerCase().includes(filters.district!.toLowerCase())
      );
    }

    if (filters.rating) {
      filteredProperties = filteredProperties.filter(p => p.rating >= filters.rating!);
    }

    if (filters.capacity) {
      filteredProperties = filteredProperties.filter(p => p.capacity >= filters.capacity!);
    }

    return {
      success: true,
      data: filteredProperties,
      message: `Found ${filteredProperties.length} properties`
    };
  },

  // Get property by ID
  getPropertyById: async (id: string): Promise<ApiResponse<Property>> => {
    await simulateDelay();
    simulateError();

    const property = allProperties.find(p => p.id === id);
    
    if (!property) {
      return {
        success: false,
        data: null as any,
        message: 'Property not found',
        errors: ['Property with the specified ID does not exist']
      };
    }

    return {
      success: true,
      data: property,
      message: 'Property retrieved successfully'
    };
  },

  // Search properties
  searchProperties: async (params: PropertySearchParams): Promise<SearchResponse<Property>> => {
    const startTime = Date.now();
    await simulateDelay();
    simulateError();

    let results = [...allProperties];

    // Apply text search
    if (params.query) {
      const query = params.query.toLowerCase();
      results = results.filter(p => 
        p.name.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query) ||
        p.location.district.toLowerCase().includes(query) ||
        p.location.address.toLowerCase().includes(query)
      );
    }

    // Apply filters
    if (params.filters) {
      const filtered = await propertyService.getProperties(params.filters);
      if (filtered.success) {
        const filteredIds = filtered.data.map(p => p.id);
        results = results.filter(p => filteredIds.includes(p.id));
      }
    }

    // Apply sorting
    if (params.sortBy) {
      results.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (params.sortBy) {
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'rating':
            aValue = a.rating;
            bValue = b.rating;
            break;
          case 'newest':
            aValue = new Date(a.createdAt).getTime();
            bValue = new Date(b.createdAt).getTime();
            break;
          default:
            return 0;
        }

        if (params.sortOrder === 'desc') {
          return bValue - aValue;
        }
        return aValue - bValue;
      });
    }

    const paginatedResults = paginate(results, params.page, params.limit);
    const searchTime = Date.now() - startTime;

    return {
      ...paginatedResults,
      query: params.query,
      filters: params.filters,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      searchTime
    };
  },

  // Get featured properties
  getFeaturedProperties: async (): Promise<ApiResponse<Property[]>> => {
    await simulateDelay();
    simulateError();

    const featuredProperties = allProperties
      .filter(p => p.rating >= 4.5 && p.reviewCount >= 30)
      .slice(0, 6);

    return {
      success: true,
      data: featuredProperties,
      message: `Found ${featuredProperties.length} featured properties`
    };
  }
};

// User Service
export const userService = {
  // Get user by ID
  getUserById: async (id: string): Promise<ApiResponse<User>> => {
    await simulateDelay();
    simulateError();

    const user = mockUsers.find(u => u.id === id);
    
    if (!user) {
      return {
        success: false,
        data: null as any,
        message: 'User not found',
        errors: ['User with the specified ID does not exist']
      };
    }

    return {
      success: true,
      data: user,
      message: 'User retrieved successfully'
    };
  },

  // Get all users (admin only)
  getAllUsers: async (): Promise<ApiResponse<User[]>> => {
    await simulateDelay();
    simulateError();

    return {
      success: true,
      data: mockUsers,
      message: `Found ${mockUsers.length} users`
    };
  }
};

// Booking Service
export const bookingService = {
  // Get user bookings
  getUserBookings: async (userId: string, filters?: BookingFilters): Promise<ApiResponse<Booking[]>> => {
    await simulateDelay();
    simulateError();

    let userBookings = mockBookings.filter(b => b.userId === userId);

    // Apply filters
    if (filters?.status && filters.status.length > 0) {
      userBookings = userBookings.filter(b => filters.status!.includes(b.status));
    }

    if (filters?.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      userBookings = userBookings.filter(b =>
        b.checkIn >= startDate && b.checkOut <= endDate
      );
    }

    if (filters?.propertyType && filters.propertyType.length > 0) {
      userBookings = userBookings.filter(b => filters.propertyType!.includes(b.propertyType));
    }

    if (filters?.priceRange) {
      const [min, max] = filters.priceRange;
      userBookings = userBookings.filter(b => b.totalPrice >= min && b.totalPrice <= max);
    }

    return {
      success: true,
      data: userBookings,
      message: `Found ${userBookings.length} bookings`
    };
  },

  // Get booking by ID
  getBookingById: async (id: string): Promise<ApiResponse<Booking>> => {
    await simulateDelay();
    simulateError();

    const booking = mockBookings.find(b => b.id === id);

    if (!booking) {
      return {
        success: false,
        data: null as any,
        message: 'Booking not found',
        errors: ['Booking with the specified ID does not exist']
      };
    }

    return {
      success: true,
      data: booking,
      message: 'Booking retrieved successfully'
    };
  },

  // Create new booking
  createBooking: async (bookingData: BookingRequest): Promise<ApiResponse<Booking>> => {
    await simulateDelay();
    simulateError();

    // Validate property exists
    const property = allProperties.find(p => p.id === bookingData.propertyId);
    if (!property) {
      return {
        success: false,
        data: null as any,
        message: 'Property not found',
        errors: ['Property with the specified ID does not exist']
      };
    }

    // Calculate pricing
    const checkIn = new Date(bookingData.checkIn);
    const checkOut = new Date(bookingData.checkOut);
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));

    const subtotal = property.price * nights;
    const serviceFee = subtotal * 0.05;
    const taxes = subtotal * 0.10;
    const total = subtotal + serviceFee + taxes;

    // Create new booking
    const newBooking: Booking = {
      id: `booking_${Date.now()}`,
      propertyId: bookingData.propertyId,
      propertyName: property.name,
      propertyImage: property.images[0],
      propertyType: property.type,
      userId: 'current_user', // Would come from auth context
      userName: 'Current User',
      userEmail: '<EMAIL>',
      userPhone: '+62812345678',
      checkIn: bookingData.checkIn,
      checkOut: bookingData.checkOut,
      guests: bookingData.guests,
      totalPrice: total,
      status: 'pending',
      paymentStatus: 'pending',
      paymentMethod: bookingData.paymentMethod as any,
      specialRequests: bookingData.specialRequests,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      priceBreakdown: {
        basePrice: property.price,
        nights,
        subtotal,
        serviceFee,
        taxes,
        cleaningFee: 0,
        discount: 0,
        total
      },
      guestDetails: bookingData.guestDetails,
      contactInfo: bookingData.contactInfo || {}
    };

    // Add to mock data (in real app, this would be saved to database)
    mockBookings.push(newBooking);

    return {
      success: true,
      data: newBooking,
      message: 'Booking created successfully'
    };
  }
};
