import { ApiResponse, PaginatedResponse } from '../types/api';
import { Review, ReviewRequest, ReviewFilters, ReviewSummary } from '../types/review';
import mockReviews from '../data/reviews';
import mockBookings from '../data/bookings';

// Utility functions from mockApi.ts
const simulateDelay = async (): Promise<void> => {
  const delay = Math.random() * 600 + 200; // 200-800ms
  await new Promise(resolve => setTimeout(resolve, delay));
};

const simulateError = (): void => {
  if (Math.random() < 0.05) { // 5% error rate
    throw new Error('Simulated network error');
  }
};

const paginate = <T>(data: T[], page: number = 1, limit: number = 20): PaginatedResponse<T> => {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = data.slice(startIndex, endIndex);
  
  return {
    data: paginatedData,
    meta: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
      hasNext: endIndex < data.length,
      hasPrev: page > 1
    }
  };
};

// Review Service
export const reviewService = {
  // Get reviews for a property
  getPropertyReviews: async (propertyId: string, filters?: ReviewFilters): Promise<ApiResponse<Review[]>> => {
    await simulateDelay();
    simulateError();

    let propertyReviews = mockReviews.filter(r => r.propertyId === propertyId);

    // Apply filters
    if (filters?.rating && filters.rating.length > 0) {
      propertyReviews = propertyReviews.filter(r => filters.rating!.includes(r.rating));
    }

    if (filters?.travelType && filters.travelType.length > 0) {
      propertyReviews = propertyReviews.filter(r => filters.travelType!.includes(r.travelType));
    }

    if (filters?.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      propertyReviews = propertyReviews.filter(r => 
        r.createdAt >= startDate && r.createdAt <= endDate
      );
    }

    if (filters?.hasImages !== undefined) {
      propertyReviews = propertyReviews.filter(r => 
        filters.hasImages ? (r.images && r.images.length > 0) : (!r.images || r.images.length === 0)
      );
    }

    if (filters?.verified !== undefined) {
      propertyReviews = propertyReviews.filter(r => r.isVerified === filters.verified);
    }

    return {
      success: true,
      data: propertyReviews,
      message: `Found ${propertyReviews.length} reviews`
    };
  },

  // Get review by ID
  getReviewById: async (id: string): Promise<ApiResponse<Review>> => {
    await simulateDelay();
    simulateError();

    const review = mockReviews.find(r => r.id === id);
    
    if (!review) {
      return {
        success: false,
        data: null as any,
        message: 'Review not found',
        errors: ['Review with the specified ID does not exist']
      };
    }

    return {
      success: true,
      data: review,
      message: 'Review retrieved successfully'
    };
  },

  // Create new review
  createReview: async (reviewData: ReviewRequest): Promise<ApiResponse<Review>> => {
    await simulateDelay();
    simulateError();

    // Validate booking exists and is completed
    const booking = mockBookings.find(b => b.id === reviewData.bookingId);
    if (!booking) {
      return {
        success: false,
        data: null as any,
        message: 'Booking not found',
        errors: ['Booking with the specified ID does not exist']
      };
    }

    if (booking.status !== 'completed') {
      return {
        success: false,
        data: null as any,
        message: 'Cannot review incomplete booking',
        errors: ['Only completed bookings can be reviewed']
      };
    }

    // Check if review already exists for this booking
    const existingReview = mockReviews.find(r => r.bookingId === reviewData.bookingId);
    if (existingReview) {
      return {
        success: false,
        data: null as any,
        message: 'Review already exists',
        errors: ['A review has already been submitted for this booking']
      };
    }

    // Create new review
    const newReview: Review = {
      id: `review_${Date.now()}`,
      bookingId: reviewData.bookingId,
      propertyId: booking.propertyId,
      propertyName: booking.propertyName,
      userId: booking.userId,
      userName: booking.userName,
      userAvatar: undefined, // Would come from user data
      rating: reviewData.rating,
      title: reviewData.title,
      comment: reviewData.comment,
      images: reviewData.images,
      createdAt: new Date().toISOString(),
      isVerified: true, // Auto-verify for completed bookings
      helpfulCount: 0,
      categories: reviewData.categories,
      stayDuration: Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24)),
      travelType: reviewData.travelType,
      wouldRecommend: reviewData.wouldRecommend
    };

    // Add to mock data
    mockReviews.push(newReview);

    return {
      success: true,
      data: newReview,
      message: 'Review created successfully'
    };
  },

  // Get review summary for a property
  getReviewSummary: async (propertyId: string): Promise<ApiResponse<ReviewSummary>> => {
    await simulateDelay();
    simulateError();

    const propertyReviews = mockReviews.filter(r => r.propertyId === propertyId);

    if (propertyReviews.length === 0) {
      return {
        success: true,
        data: {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
          categoryAverages: {
            cleanliness: 0,
            accuracy: 0,
            communication: 0,
            location: 0,
            checkIn: 0,
            value: 0
          },
          recommendationRate: 0
        },
        message: 'No reviews found for this property'
      };
    }

    const summary: ReviewSummary = {
      averageRating: propertyReviews.reduce((sum, r) => sum + r.rating, 0) / propertyReviews.length,
      totalReviews: propertyReviews.length,
      ratingDistribution: {
        5: propertyReviews.filter(r => r.rating === 5).length,
        4: propertyReviews.filter(r => r.rating === 4).length,
        3: propertyReviews.filter(r => r.rating === 3).length,
        2: propertyReviews.filter(r => r.rating === 2).length,
        1: propertyReviews.filter(r => r.rating === 1).length,
      },
      categoryAverages: {
        cleanliness: propertyReviews.reduce((sum, r) => sum + r.categories.cleanliness, 0) / propertyReviews.length,
        accuracy: propertyReviews.reduce((sum, r) => sum + r.categories.accuracy, 0) / propertyReviews.length,
        communication: propertyReviews.reduce((sum, r) => sum + r.categories.communication, 0) / propertyReviews.length,
        location: propertyReviews.reduce((sum, r) => sum + r.categories.location, 0) / propertyReviews.length,
        checkIn: propertyReviews.reduce((sum, r) => sum + r.categories.checkIn, 0) / propertyReviews.length,
        value: propertyReviews.reduce((sum, r) => sum + r.categories.value, 0) / propertyReviews.length,
      },
      recommendationRate: propertyReviews.filter(r => r.wouldRecommend).length / propertyReviews.length
    };

    return {
      success: true,
      data: summary,
      message: 'Review summary retrieved successfully'
    };
  },

  // Mark review as helpful
  markReviewHelpful: async (reviewId: string): Promise<ApiResponse<Review>> => {
    await simulateDelay();
    simulateError();

    const reviewIndex = mockReviews.findIndex(r => r.id === reviewId);
    
    if (reviewIndex === -1) {
      return {
        success: false,
        data: null as any,
        message: 'Review not found',
        errors: ['Review with the specified ID does not exist']
      };
    }

    const review = mockReviews[reviewIndex];
    review.helpfulCount += 1;

    return {
      success: true,
      data: review,
      message: 'Review marked as helpful'
    };
  }
};

// Analytics Service
export const analyticsService = {
  // Get property analytics
  getPropertyAnalytics: async (propertyId: string): Promise<ApiResponse<any>> => {
    await simulateDelay();
    simulateError();

    const propertyBookings = mockBookings.filter(b => b.propertyId === propertyId);
    const propertyReviews = mockReviews.filter(r => r.propertyId === propertyId);

    const analytics = {
      totalBookings: propertyBookings.length,
      completedBookings: propertyBookings.filter(b => b.status === 'completed').length,
      cancelledBookings: propertyBookings.filter(b => b.status === 'cancelled').length,
      totalRevenue: propertyBookings
        .filter(b => b.paymentStatus === 'paid')
        .reduce((sum, b) => sum + b.totalPrice, 0),
      averageBookingValue: propertyBookings.length > 0 
        ? propertyBookings.reduce((sum, b) => sum + b.totalPrice, 0) / propertyBookings.length 
        : 0,
      occupancyRate: 0.75, // Mock data
      averageStayDuration: propertyBookings.length > 0
        ? propertyBookings.reduce((sum, b) => {
            const nights = Math.ceil((new Date(b.checkOut).getTime() - new Date(b.checkIn).getTime()) / (1000 * 60 * 60 * 24));
            return sum + nights;
          }, 0) / propertyBookings.length
        : 0,
      reviewStats: {
        totalReviews: propertyReviews.length,
        averageRating: propertyReviews.length > 0 
          ? propertyReviews.reduce((sum, r) => sum + r.rating, 0) / propertyReviews.length 
          : 0,
        recommendationRate: propertyReviews.length > 0
          ? propertyReviews.filter(r => r.wouldRecommend).length / propertyReviews.length
          : 0
      }
    };

    return {
      success: true,
      data: analytics,
      message: 'Property analytics retrieved successfully'
    };
  }
};

// Export all services
export default {
  reviewService,
  analyticsService
};
