{"compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true, "moduleResolution": "node", "allowJs": true, "noEmit": true, "isolatedModules": true, "jsx": "react-native", "baseUrl": "./", "paths": {"@/*": ["./*"], "@data/*": ["./data/*"], "@types/*": ["./types/*"], "@services/*": ["./services/*"], "@utils/*": ["./utils/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"], "extends": "expo/tsconfig.base"}