export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SearchResponse<T> extends PaginatedResponse<T> {
  query?: string;
  filters?: any;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  searchTime: number; // in milliseconds
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: string[];
  code?: string;
  statusCode: number;
}

export interface MockApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  simulateNetworkDelay: boolean;
  networkDelayRange: [number, number]; // min, max in ms
  errorRate: number; // 0-1, probability of random errors
}

export interface MockEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  handler: (params?: any, body?: any, query?: any) => Promise<any>;
  delay?: number;
  errorRate?: number;
}
