export interface User {
  id: string;
  email: string;
  name: string;
  phone: string;
  avatar?: string;
  role: 'guest' | 'host' | 'admin';
  createdAt: string;
  updatedAt?: string;
  isVerified: boolean;
  bio?: string;
  location?: string;
  joinedDate: string;
  totalBookings?: number;
  totalReviews?: number;
  averageRating?: number;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
}

export interface RegisterData {
  name: string;
  email: string;
  phone: string;
  password: string;
  role: 'guest' | 'host';
}

export interface LoginData {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}
