export interface Booking {
  id: string;
  propertyId: string;
  propertyName: string;
  propertyImage: string;
  propertyType: string;
  userId: string;
  userName: string;
  userEmail: string;
  userPhone: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'in_progress';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partial';
  paymentMethod?: 'bank_transfer' | 'credit_card' | 'e_wallet' | 'cash';
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
  confirmedAt?: string;
  cancelledAt?: string;
  cancellationReason?: string;
  priceBreakdown: {
    basePrice: number;
    nights: number;
    subtotal: number;
    serviceFee: number;
    taxes: number;
    cleaningFee?: number;
    discount?: number;
    total: number;
  };
  guestDetails: {
    adults: number;
    children: number;
    infants: number;
  };
  contactInfo: {
    emergencyContact?: string;
    estimatedArrival?: string;
  };
}

export interface BookingRequest {
  propertyId: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  guestDetails: {
    adults: number;
    children: number;
    infants: number;
  };
  specialRequests?: string;
  paymentMethod: string;
  contactInfo?: {
    emergencyContact?: string;
    estimatedArrival?: string;
  };
}

export interface BookingFilters {
  status?: string[];
  dateRange?: [string, string];
  propertyType?: string[];
  priceRange?: [number, number];
}
