// Export all types from individual modules
export * from './auth';
export * from './property';
export * from './booking';
export * from './review';
export * from './tourism';
export * from './api';

// Common utility types
export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Address {
  street: string;
  district: string;
  subDistrict?: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
}

export interface ImageData {
  id: string;
  url: string;
  alt?: string;
  caption?: string;
  width?: number;
  height?: number;
  size?: number; // in bytes
}

export interface ContactInfo {
  phone: string;
  email?: string;
  whatsapp?: string;
  website?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
  };
}

export interface BusinessHours {
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
}

export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

export interface Rating {
  average: number;
  count: number;
  distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// Indonesian specific types
export interface IndonesianPhoneNumber {
  countryCode: '+62';
  number: string; // without country code
  formatted: string; // with country code and formatting
}

export interface IndonesianCurrency {
  amount: number;
  currency: 'IDR';
  formatted: string; // e.g., "Rp 150.000"
}

export interface JogjaSpecific {
  sultanateArea: boolean; // if in Kraton area
  culturalSignificance: string[];
  traditionalName?: string; // traditional Javanese name
  historicalPeriod?: string;
}
