export interface Property {
  id: string;
  name: string;
  type: 'homestay' | 'guesthouse' | 'hotel' | 'villa';
  description: string;
  images: string[];
  price: number;
  location: {
    address: string;
    latitude: number;
    longitude: number;
    district: string;
    subDistrict?: string;
    postalCode?: string;
  };
  amenities: string[];
  rooms: number;
  bathrooms: number;
  capacity: number;
  rating: number;
  reviewCount: number;
  hostId: string;
  hostName: string;
  hostAvatar?: string;
  hostJoinedDate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
  checkInTime: string;
  checkOutTime: string;
  houseRules: string[];
  nearbyAttractions: NearbyAttraction[];
  policies: {
    cancellation: string;
    checkIn: string;
    checkOut: string;
    smoking: boolean;
    pets: boolean;
    parties: boolean;
  };
  priceBreakdown?: {
    basePrice: number;
    cleaningFee?: number;
    serviceFee?: number;
    taxes?: number;
  };
}

export interface NearbyAttraction {
  id: string;
  name: string;
  type: 'cultural' | 'historical' | 'culinary' | 'nature' | 'shopping';
  distance: number; // in kilometers
  walkingTime?: number; // in minutes
}

export interface PropertyFilters {
  type?: string[];
  priceRange?: [number, number];
  amenities?: string[];
  location?: string;
  district?: string;
  rating?: number;
  capacity?: number;
  checkIn?: string;
  checkOut?: string;
}

export interface PropertySearchParams {
  query?: string;
  filters?: PropertyFilters;
  sortBy?: 'price' | 'rating' | 'distance' | 'newest';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}
