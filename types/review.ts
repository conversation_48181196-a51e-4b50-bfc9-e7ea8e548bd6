export interface Review {
  id: string;
  bookingId: string;
  propertyId: string;
  propertyName: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  createdAt: string;
  updatedAt?: string;
  isVerified: boolean;
  helpfulCount: number;
  hostReply?: {
    message: string;
    createdAt: string;
  };
  categories: {
    cleanliness: number;
    accuracy: number;
    communication: number;
    location: number;
    checkIn: number;
    value: number;
  };
  stayDuration: number; // in nights
  travelType: 'business' | 'leisure' | 'family' | 'couple' | 'solo' | 'friends';
  wouldRecommend: boolean;
}

export interface ReviewRequest {
  bookingId: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  categories: {
    cleanliness: number;
    accuracy: number;
    communication: number;
    location: number;
    checkIn: number;
    value: number;
  };
  travelType: 'business' | 'leisure' | 'family' | 'couple' | 'solo' | 'friends';
  wouldRecommend: boolean;
}

export interface ReviewFilters {
  rating?: number[];
  travelType?: string[];
  dateRange?: [string, string];
  hasImages?: boolean;
  verified?: boolean;
}

export interface ReviewSummary {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
  categoryAverages: {
    cleanliness: number;
    accuracy: number;
    communication: number;
    location: number;
    checkIn: number;
    value: number;
  };
  recommendationRate: number;
}
