export interface TourismSpot {
  id: string;
  name: string;
  type: 'cultural' | 'historical' | 'culinary' | 'nature' | 'shopping' | 'entertainment';
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    district: string;
  };
  images: string[];
  rating: number;
  reviewCount: number;
  openingHours: {
    [key: string]: string; // day: hours
  };
  ticketPrice?: {
    adult: number;
    child: number;
    student?: number;
    foreigner?: number;
  };
  facilities: string[];
  tags: string[];
  isPopular: boolean;
  distance?: number; // from property
  walkingTime?: number; // in minutes
  transportOptions?: string[];
}

export interface CulinarySpot {
  id: string;
  name: string;
  type: 'restaurant' | 'cafe' | 'street_food' | 'traditional' | 'warung' | 'food_court';
  specialties: string[];
  priceRange: 'budget' | 'mid' | 'expensive';
  location: {
    latitude: number;
    longitude: number;
    address: string;
    district: string;
  };
  rating: number;
  reviewCount: number;
  openingHours: {
    [key: string]: string;
  };
  images: string[];
  averagePrice: number;
  isHalal: boolean;
  hasDelivery: boolean;
  acceptsCards: boolean;
  distance?: number;
  walkingTime?: number;
  popularDishes: string[];
  ambiance: string[];
}

export interface District {
  id: string;
  name: string;
  description: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  popularFor: string[];
  averagePropertyPrice: number;
  transportAccess: string[];
  nearbyAttractions: string[];
  characteristics: string[];
}

export interface JogjaLocation {
  districts: District[];
  popularAreas: string[];
  transportHubs: {
    name: string;
    type: 'airport' | 'train_station' | 'bus_terminal';
    coordinates: {
      latitude: number;
      longitude: number;
    };
  }[];
}
