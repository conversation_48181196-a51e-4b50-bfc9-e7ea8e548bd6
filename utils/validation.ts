// Data validation functions for JogjaHub mock data

import { Property } from '../types/property';
import { User } from '../types/auth';
import { Booking } from '../types/booking';
import { Review } from '../types/review';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Indonesian phone number validation
export const validateIndonesianPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
  return phoneRegex.test(phone);
};

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Price validation (IDR)
export const validatePrice = (price: number): boolean => {
  return price >= 50000 && price <= 10000000; // 50k - 10M IDR
};

// Coordinates validation (Yogyakarta area)
export const validateYogyaCoordinates = (lat: number, lng: number): boolean => {
  // Rough bounds for Yogyakarta area
  const bounds = {
    north: -7.5,
    south: -8.2,
    east: 110.8,
    west: 110.0
  };
  
  return lat >= bounds.south && lat <= bounds.north && 
         lng >= bounds.west && lng <= bounds.east;
};

// Date validation
export const validateDateRange = (checkIn: string, checkOut: string): boolean => {
  const checkInDate = new Date(checkIn);
  const checkOutDate = new Date(checkOut);
  const today = new Date();
  
  return checkInDate >= today && checkOutDate > checkInDate;
};

// Property validation
export const validateProperty = (property: Property): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!property.id) errors.push('Property ID is required');
  if (!property.name || property.name.length < 3) errors.push('Property name must be at least 3 characters');
  if (!property.description || property.description.length < 20) errors.push('Property description must be at least 20 characters');
  if (!property.type || !['homestay', 'guesthouse', 'hotel', 'villa'].includes(property.type)) {
    errors.push('Property type must be homestay, guesthouse, hotel, or villa');
  }

  // Price validation
  if (!validatePrice(property.price)) {
    errors.push('Property price must be between 50,000 and 10,000,000 IDR');
  }

  // Location validation
  if (!property.location.address) errors.push('Property address is required');
  if (!validateYogyaCoordinates(property.location.latitude, property.location.longitude)) {
    errors.push('Property coordinates must be within Yogyakarta area');
  }
  if (!property.location.district) errors.push('Property district is required');

  // Images validation
  if (!property.images || property.images.length === 0) {
    errors.push('Property must have at least one image');
  } else if (property.images.length > 10) {
    warnings.push('Property has more than 10 images, consider reducing for better performance');
  }

  // Amenities validation
  if (!property.amenities || property.amenities.length === 0) {
    warnings.push('Property should have at least some amenities listed');
  }

  // Capacity validation
  if (property.capacity < 1 || property.capacity > 20) {
    errors.push('Property capacity must be between 1 and 20 guests');
  }

  // Rating validation
  if (property.rating < 1 || property.rating > 5) {
    errors.push('Property rating must be between 1 and 5');
  }

  // Review count validation
  if (property.reviewCount < 0) {
    errors.push('Review count cannot be negative');
  }

  // Host validation
  if (!property.hostId) errors.push('Property must have a host ID');
  if (!property.hostName) errors.push('Property must have a host name');

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// User validation
export const validateUser = (user: User): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!user.id) errors.push('User ID is required');
  if (!user.email || !validateEmail(user.email)) errors.push('Valid email is required');
  if (!user.name || user.name.length < 2) errors.push('User name must be at least 2 characters');
  if (!user.phone || !validateIndonesianPhone(user.phone)) {
    errors.push('Valid Indonesian phone number is required');
  }
  if (!user.role || !['guest', 'host', 'admin'].includes(user.role)) {
    errors.push('User role must be guest, host, or admin');
  }

  // Date validation
  if (!user.createdAt) errors.push('User creation date is required');
  if (user.joinedDate && new Date(user.joinedDate) > new Date()) {
    errors.push('User join date cannot be in the future');
  }

  // Rating validation for hosts
  if (user.role === 'host' && user.averageRating) {
    if (user.averageRating < 1 || user.averageRating > 5) {
      errors.push('Host average rating must be between 1 and 5');
    }
  }

  // Booking count validation
  if (user.totalBookings && user.totalBookings < 0) {
    errors.push('Total bookings cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Booking validation
export const validateBooking = (booking: Booking): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!booking.id) errors.push('Booking ID is required');
  if (!booking.propertyId) errors.push('Property ID is required');
  if (!booking.userId) errors.push('User ID is required');
  if (!booking.userName) errors.push('User name is required');
  if (!booking.userEmail || !validateEmail(booking.userEmail)) {
    errors.push('Valid user email is required');
  }
  if (!booking.userPhone || !validateIndonesianPhone(booking.userPhone)) {
    errors.push('Valid user phone number is required');
  }

  // Date validation
  if (!validateDateRange(booking.checkIn, booking.checkOut)) {
    errors.push('Check-in must be today or later, and check-out must be after check-in');
  }

  // Guest validation
  if (booking.guests < 1 || booking.guests > 20) {
    errors.push('Number of guests must be between 1 and 20');
  }

  // Price validation
  if (booking.totalPrice < 50000) {
    errors.push('Total price seems too low for Indonesian accommodation');
  }

  // Status validation
  const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed', 'in_progress'];
  if (!validStatuses.includes(booking.status)) {
    errors.push('Invalid booking status');
  }

  // Payment status validation
  const validPaymentStatuses = ['pending', 'paid', 'failed', 'refunded', 'partial'];
  if (!validPaymentStatuses.includes(booking.paymentStatus)) {
    errors.push('Invalid payment status');
  }

  // Price breakdown validation
  if (booking.priceBreakdown) {
    const { basePrice, nights, subtotal, serviceFee, taxes, total } = booking.priceBreakdown;
    
    if (basePrice * nights !== subtotal) {
      errors.push('Price breakdown calculation error: basePrice * nights ≠ subtotal');
    }
    
    const calculatedTotal = subtotal + serviceFee + taxes + (booking.priceBreakdown.cleaningFee || 0) - (booking.priceBreakdown.discount || 0);
    if (Math.abs(calculatedTotal - total) > 1) { // Allow 1 IDR difference for rounding
      errors.push('Price breakdown calculation error: total does not match sum of components');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Review validation
export const validateReview = (review: Review): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!review.id) errors.push('Review ID is required');
  if (!review.bookingId) errors.push('Booking ID is required');
  if (!review.propertyId) errors.push('Property ID is required');
  if (!review.userId) errors.push('User ID is required');
  if (!review.userName) errors.push('User name is required');

  // Rating validation
  if (review.rating < 1 || review.rating > 5) {
    errors.push('Review rating must be between 1 and 5');
  }

  // Content validation
  if (!review.title || review.title.length < 5) {
    errors.push('Review title must be at least 5 characters');
  }
  if (!review.comment || review.comment.length < 10) {
    errors.push('Review comment must be at least 10 characters');
  }

  // Category ratings validation
  if (review.categories) {
    const categories = ['cleanliness', 'accuracy', 'communication', 'location', 'checkIn', 'value'];
    categories.forEach(category => {
      const rating = review.categories[category as keyof typeof review.categories];
      if (rating < 1 || rating > 5) {
        errors.push(`Category rating for ${category} must be between 1 and 5`);
      }
    });
  }

  // Stay duration validation
  if (review.stayDuration < 1 || review.stayDuration > 365) {
    errors.push('Stay duration must be between 1 and 365 days');
  }

  // Travel type validation
  const validTravelTypes = ['business', 'leisure', 'family', 'couple', 'solo', 'friends'];
  if (!validTravelTypes.includes(review.travelType)) {
    errors.push('Invalid travel type');
  }

  // Helpful count validation
  if (review.helpfulCount < 0) {
    errors.push('Helpful count cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Batch validation functions
export const validateAllProperties = (properties: Property[]): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  properties.forEach((property, index) => {
    const result = validateProperty(property);
    if (!result.isValid) {
      allErrors.push(`Property ${index + 1} (${property.id}): ${result.errors.join(', ')}`);
    }
    if (result.warnings.length > 0) {
      allWarnings.push(`Property ${index + 1} (${property.id}): ${result.warnings.join(', ')}`);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

export const validateAllUsers = (users: User[]): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  users.forEach((user, index) => {
    const result = validateUser(user);
    if (!result.isValid) {
      allErrors.push(`User ${index + 1} (${user.id}): ${result.errors.join(', ')}`);
    }
    if (result.warnings.length > 0) {
      allWarnings.push(`User ${index + 1} (${user.id}): ${result.warnings.join(', ')}`);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

export const validateAllBookings = (bookings: Booking[]): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  bookings.forEach((booking, index) => {
    const result = validateBooking(booking);
    if (!result.isValid) {
      allErrors.push(`Booking ${index + 1} (${booking.id}): ${result.errors.join(', ')}`);
    }
    if (result.warnings.length > 0) {
      allWarnings.push(`Booking ${index + 1} (${booking.id}): ${result.warnings.join(', ')}`);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

export const validateAllReviews = (reviews: Review[]): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  reviews.forEach((review, index) => {
    const result = validateReview(review);
    if (!result.isValid) {
      allErrors.push(`Review ${index + 1} (${review.id}): ${result.errors.join(', ')}`);
    }
    if (result.warnings.length > 0) {
      allWarnings.push(`Review ${index + 1} (${review.id}): ${result.warnings.join(', ')}`);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};
